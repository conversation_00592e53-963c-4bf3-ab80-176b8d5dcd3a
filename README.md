# 👁️ Gaze Tracking Training Pipeline

A comprehensive deep learning pipeline for training gaze estimation models using PyTorch. This repository provides multiple training strategies, model architectures, and deployment optimizations for accurate gaze direction prediction.

## 🎯 Overview

This project implements state-of-the-art gaze estimation models that predict gaze direction (pitch and yaw angles) from face images. The pipeline supports multiple training strategies, datasets, and model architectures with a focus on both accuracy and deployment efficiency.

### Key Features

- **Multiple Training Strategies**: Default, MPII, and Fusion training approaches
- **Advanced Model Architectures**: VGG-based custom models and fusion-optimized networks
- **Deployment Optimization**: Model fusion for reduced FLOPs and faster inference
- **Comprehensive Data Pipeline**: Support for multiple datasets with advanced augmentation
- **Production Ready**: ONNX export and deployment-optimized models

## 📁 Project Structure

```
├── train_subject.py              # Default training strategy
├── train_subject_mpii.py         # MPII dataset training
├── train_subject_fusion.py       # Fusion model training with deployment
├── models/                       # Model architectures
│   ├── vgg_face_subject.py      # Standard VGG-based gaze model
│   ├── vgg_face_subject_fusion_training.py   # Fusion training model
│   ├── vgg_face_subject_fusion_deployment.py # Optimized deployment model
│   ├── resnet.py                # ResNet architectures
│   ├── mobilenet.py             # MobileNet architectures
│   └── ...                      # Other model variants
├── utils/                       # Utilities and data handling
│   ├── datasets_subject.py     # Standard dataset loader
│   ├── datasets_subject_mpii.py # MPII dataset loader
│   ├── helpers.py               # Helper functions and metrics
│   ├── util.py                  # Training utilities
│   └── augmentation.py          # Data augmentation pipeline
├── data/                        # Dataset directories
├── weights/                     # Pre-trained model weights
├── runs/                        # Training experiment outputs
└── assets/                      # Demo videos and visualizations
```

## 🚀 Quick Start

### Prerequisites

Create the conda environment:

```bash
conda env create -f requirements.yml
conda activate gaze-dev
```

### Training Models

#### 1. Default Training Strategy
```bash
python train_subject.py --train --test
```

#### 2. MPII Dataset Training
```bash
python train_subject_mpii.py --train --test
```

#### 3. Fusion Model Training (Deployment Optimized)
```bash
python train_subject_fusion.py --train --test
```

## 🏗️ Model Architectures

### 1. VGG-Based Gaze Model (`FinalModel`)

A custom VGG16-based architecture optimized for gaze estimation:

- **Backbone**: VGG16 features with dilated convolutions
- **Input**: 96×96 RGB face images
- **Output**: 2D gaze angles (pitch, yaw)
- **Features**: Batch normalization, dropout regularization
- **FLOPs**: ~2.5 GFLOPs

### 2. Fusion Models (`FinalModelFusion` + `FinalModelDeploy`)

Advanced fusion architecture for deployment optimization:

- **Training Model**: Uses fusable Conv3XC blocks
- **Deployment Model**: Fused 3×3 convolutions for efficiency
- **Optimization**: Automatic model fusion for reduced inference time
- **FLOPs**: ~1.2 GFLOPs (after fusion)

## 📊 Dataset Support

### Supported Datasets

1. **Custom Merged Dataset**: `dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5`
2. **MPIIFaceGaze**: `MPIIFaceGaze_96_scaleTrue_FDN500-600`
3. **Multiple Variants**: Various preprocessing and scaling options

### Dataset Format

Expected directory structure:
```
data/
├── labels.csv                   # Gaze labels and metadata
└── pXX/                        # Person-specific directories
    └── <basename>-face.jpg     # Face images (96×96)
```

CSV format:
- `face_file_name`: Relative path to face image
- `pitch`: Gaze pitch angle in radians
- `yaw`: Gaze yaw angle in radians
- `normalized_*_eye_center_*`: Eye center coordinates

## 🔧 Training Configuration

### Loss Functions

The pipeline uses a combination of loss functions:

```python
# Angular loss (optional)
loss_ang, angular_error = calculate_gaze_angle_error(labels, outputs)

# MSE loss
mse_loss = reg_criterion(labels, outputs)

# Combined loss
loss = (args.alpha * loss_ang) + (args.beta * mse_loss)
```

### Optimization

- **Optimizer**: Adam with configurable parameters
- **Scheduler**: ReduceLROnPlateau for adaptive learning rate
- **Regularization**: Dropout, batch normalization
- **Early Stopping**: Based on validation angular error

### Data Augmentation

Advanced augmentation pipeline using Albumentations:
- Random brightness/contrast adjustment
- Gaussian noise injection
- Geometric transformations
- CLAHE histogram equalization

## 📈 Training Strategies

### 1. Default Strategy
- **Dataset**: Custom merged dataset
- **Batch Size**: 32
- **Model**: Standard VGG-based architecture
- **Use Case**: General-purpose gaze estimation

### 2. MPII Strategy
- **Dataset**: MPIIFaceGaze
- **Batch Size**: 256 (larger for better convergence)
- **Model**: Standard VGG-based architecture
- **Use Case**: Use to pre-train models on open-source datasets

### 3. Fusion Strategy
- **Dataset**: Custom merged dataset
- **Batch Size**: 32
- **Model**: Fusion architecture with deployment optimization
- **Use Case**: Production deployment with efficiency requirements

## 🎯 Performance Metrics

The pipeline tracks multiple metrics:

- **Angular Error**: Mean angular error in degrees (primary metric)
- **MSE Loss**: Mean squared error on gaze angles
- **Training/Validation Loss**: For monitoring overfitting
- **Learning Rate**: Adaptive scheduling based on validation performance

## 🚀 Deployment

### ONNX Export

All models support ONNX export for cross-platform deployment:

```python
# Automatic ONNX export during training
example_input = torch.randn(1, 3, 96, 96).to(device)
onnx_output_path = os.path.join(args.save_dir, 'best_model.onnx')
util.export_onnx_model(args, model, example_input, onnx_output_path)
```

### Fusion Model Deployment

For fusion models, automatic deployment optimization:

```python
# Create optimized deployment model
fused_model = model.from_fusable(src=model, dst=FinalModelDeploy())
torch.save(fused_model.state_dict(), 'best_model_fused.pt')

# Load for inference
model = FinalModelDeploy()
model.load_state_dict(torch.load('best_model_fused.pt'))
model.eval()
```

## 📊 Experiment Tracking

Each training run creates a structured experiment directory:

```
runs/exp_XXX/
├── best_model.pt                  # Best model weights
├── best_model.onnx                # Best model ONNX export
├── performance_results_loss.svg   # Performance visualizations Loss plot
├── performance_results_error.svg  # Performance visualizations Angular error plot
└── training_config_pipeline.yaml  # Training configuration
```

## 📋 Experiment Results

| expID   | Angular Vector Error (°) | Notes                                                |
| ------- | ------------------------ | ---------------------------------------------------- |
| exp_0   | 3.3183494                | train_subject-VGG16-face                             |
| exp_1   | 3.6202209°               | train_subject-VGG16-face-GT                          |
| exp_2   | 3.6078441°               | train_binned-B:20:3:30-VGG16-face                    |
| exp_3   | 3.1144831° *             | train_binned-B:20:3:30-VGG16-face-GT                 |
| exp_34  | 2.3902186                | train_subject-VGG16-face_Scaled                      |
| exp_35  | 2.6296784°               | train_binned-B:20:3:30-VGG16-face_Scaled             |
| exp_90  | 2.5627006°               | train_binned-B:10:3:15-VGG16-face_Scaled             |
| exp_91  | 2.5470232°               | train_binned-B:15:2:15-VGG16-face_Scaled             |
| exp_92  | 2.4892533°               | train_binned-B:30:1:15-VGG16-face_Scaled             |
| exp_95  | 2.4181837°               | train_binned-B:30:1:15-VGG16-face_Scaled_A-B         |
| exp_98  | 2.1570606° *             | train_binned-B:30:1:15-VGG16-face_Scaled_0-B         |
| exp_100 | 2.2851220°               | train_subject-VGG16-face_Scaled_0-MSE                |
| exp_112 | 2.3985365°               | train_subject-VGG16-face_Scaled_0-MSE                | # ENV changed |
| exp_120 | 0.6199558°               | train_binned_mpii-B:30:1:15-VGG16-face_Scaled_0-B    | # ENV changed |
| exp_121 | 2.6095882° *             | train_binned-B:30:1:15-VGG16-face_Scaled_0-B_exp-120 |


## 🛠️ Advanced Features

### Model Freezing/Unfreezing
```python
# Freeze CNN backbone initially
for param in util.get_params(model, part="cnn_face", require_grad_only=False):
    param.requires_grad = False

# Unfreeze at specific epoch
if epoch == 3:
    for param in util.get_params(model, part="fc_face", require_grad_only=False):
        param.requires_grad = True
```

### Multi-Architecture Support
The pipeline supports various architectures:
- VGG-based custom models
- ResNet variants (18, 34, 50)
- MobileNet for mobile deployment
- MobileOne for edge devices

## 📋 Command Line Arguments

### Common Arguments
- `--data`: Dataset path
- `--batch-size`: Training batch size
- `--num-epochs`: Number of training epochs
- `--lr`: Learning rate
- `--alpha`: Angular loss weight
- `--beta`: MSE loss weight
- `--train`: Enable training
- `--test`: Enable testing
- `--weights`: Path to pre-trained weights for testing

### Strategy-Specific Defaults
- **Default**: batch-size=32, data=merged_dataset
- **MPII**: batch-size=256, data=MPIIFaceGaze
- **Fusion**: batch-size=32, gamma=1.0 (additional loss weight)

## 🔍 Monitoring and Debugging

### Logging
Comprehensive logging with configurable levels:
```python
logging.info(f'Epoch [{epoch + 1}/{args.num_epochs}] '
             f'Losses: {train_loss:.4f} / {val_loss:.4f}  |  '
             f'Angular Vector Error: {train_angular_error:.1f}° / {val_angular_error:.1f}°')
```

### Visualization
Automatic generation of training curves and performance plots using matplotlib and seaborn.

## 🔗 References

- https://phi-ai.buaa.edu.cn/Gazehub/3D-dataset/#reference
- https://phi-ai.buaa.edu.cn/Gazehub/rectification/
- https://github.com/DeltaX-AI-Lab/double-sphere-camera-model/tree/main/gazepipe
- https://github.com/yakhyo/gaze-estimation
- https://github.com/xucong-zhang/ETH-XGaze
- https://github.com/X-Shi/Data-Normalization-Gaze-Estimation
- https://github.com/pperle/gaze-tracking-pipeline/blob/main/main.py
- https://www.mpi-inf.mpg.de/departments/computer-vision-and-machine-learning/research/gaze-based-human-computer-interaction/its-written-all-over-your-face-full-face-appearance-based-gaze-estimation

---
