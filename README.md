# Gaze Tracking Pipeline

A comprehensive deep learning-based gaze tracking system that estimates where a person is looking based on facial images.

## Notes

At currently, the pipeline is under development. The code is not yet fully functional and is subject to change.

## Create Conda Environment

```bash
conda env create -f requirements.yml    # can take a while... (5 minutes)
```

## Training

```bash
python train_binned.py --train --test
```

```bash
python train_subject.py --train --test
```

## Testing

```bash
python train_binned.py --test --weights runs/exp_?/best_model.pt
```

## Experiments Results

| expID   | Angular Vector Error (°) | Notes                                                |
| ------- | ------------------------ | ---------------------------------------------------- |
| exp_0   | 3.3183494                | train_subject-VGG16-face                             |
| exp_1   | 3.6202209°               | train_subject-VGG16-face-GT                          |
| exp_2   | 3.6078441°               | train_binned-B:20:3:30-VGG16-face                    |
| exp_3   | 3.1144831° *             | train_binned-B:20:3:30-VGG16-face-GT                 |
| exp_34  | 2.3902186                | train_subject-VGG16-face_Scaled                      |
| exp_35  | 2.6296784°               | train_binned-B:20:3:30-VGG16-face_Scaled             |
| exp_90  | 2.5627006°               | train_binned-B:10:3:15-VGG16-face_Scaled             |
| exp_91  | 2.5470232°               | train_binned-B:15:2:15-VGG16-face_Scaled             |
| exp_92  | 2.4892533°               | train_binned-B:30:1:15-VGG16-face_Scaled             |
| exp_95  | 2.4181837°               | train_binned-B:30:1:15-VGG16-face_Scaled_A-B         |
| exp_98  | 2.1570606° *             | train_binned-B:30:1:15-VGG16-face_Scaled_0-B         |
| exp_100 | 2.2851220°               | train_subject-VGG16-face_Scaled_0-MSE                |
| exp_112 | 2.3985365°               | train_subject-VGG16-face_Scaled_0-MSE                | # ENV changed |
| exp_120 | 0.6199558°               | train_binned_mpii-B:30:1:15-VGG16-face_Scaled_0-B    | # ENV changed |
| exp_121 | 2.6095882° *             | train_binned-B:30:1:15-VGG16-face_Scaled_0-B_exp-120 |



- https://phi-ai.buaa.edu.cn/Gazehub/3D-dataset/#reference
- https://phi-ai.buaa.edu.cn/Gazehub/rectification/
- https://github.com/DeltaX-AI-Lab/double-sphere-camera-model/tree/main/gazepipe
- https://github.com/yakhyo/gaze-estimation
- https://github.com/xucong-zhang/ETH-XGaze
- https://github.com/X-Shi/Data-Normalization-Gaze-Estimation
- https://github.com/pperle/gaze-tracking-pipeline/blob/main/main.py
- https://www.mpi-inf.mpg.de/departments/computer-vision-and-machine-learning/research/gaze-based-human-computer-interaction/its-written-all-over-your-face-full-face-appearance-based-gaze-estimation
