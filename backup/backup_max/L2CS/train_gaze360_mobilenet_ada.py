import os
import argparse
import time
import numpy as np
import torch.utils.model_zoo as model_zoo
import torch
import torch.nn as nn
from torch.autograd import Variable
from torch.utils.data import DataLoader
from torchvision import transforms
import torch.backends.cudnn as cudnn
import torchvision
import math
import pandas as pd
import json
import copy
import re

from tensorboardX import SummaryWriter
from l2cs import L2CS, select_device, Gaze360, Mpiigaze, gazeto3d, angular, MOBILEV4_ADA, MOBILEV4_ADA_v2, MOBILEV4_ADA_v3

def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description="Gaze estimation using L2CSNet.")
    # Gaze360
    parser.add_argument(
        "--gaze360image_dir",
        dest="gaze360image_dir",
        help="Directory path for gaze images.",
        default="preprocessing/gaze360/Image",
        type=str,
    )
    parser.add_argument(
        "--gaze360label_dir",
        dest="gaze360label_dir",
        help="Directory path for gaze labels.",
        default="preprocessing/gaze360/Label/train.label",
        type=str,
    )
    parser.add_argument(
        "--gaze360vallabel_dir",
        dest="gaze360vallabel_dir",
        help="Directory path for gaze labels.",
        default="preprocessing/gaze360/Label/val.label",
        type=str,
    )

    # Important args -------------------------------------------------------------------------------------------------------
    # ----------------------------------------------------------------------------------------------------------------------
    parser.add_argument(
        "--dataset",
        dest="dataset",
        help="mpiigaze, rtgene, gaze360, ethgaze",
        default="mpiigaze",
        type=str,
    )
    parser.add_argument(
        "--output",
        dest="output",
        help="Path of output models.",
        default="output_gaze360_new_strategy_pha/snapshots",
        type=str,
    )
    parser.add_argument(
        "--snapshot",
        dest="snapshot",
        help="Path of model snapshot.",
        default="",
        type=str,
    )
    parser.add_argument(
        "--gpu",
        dest="gpu_id",
        help="GPU device id to use [0] or multiple 0,1,2,3",
        default="1",
        type=str,
    )
    parser.add_argument(
        "--num_epochs",
        dest="num_epochs",
        help="Maximum number of training epochs.",
        default=60,
        type=int,
    )
    parser.add_argument(
        "--batch_size", dest="batch_size", help="Batch size.", default=32, type=int
    )
    parser.add_argument(
        "--arch",
        dest="arch",
        help="Network architecture, can be: ResNet18, ResNet34, [ResNet50], "
        "ResNet101, ResNet152, Squeezenet_1_0, Squeezenet_1_1, MobileNetV2, MobileNetV4.",
        default="ResNet50",
        type=str,
    )
    parser.add_argument(
        "--alpha",
        dest="alpha",
        help="Regression loss coefficient.",
        default=1,
        type=float,
    )
    parser.add_argument(
        "--lr", dest="lr", help="Base learning rate.", default=0.00001, type=float
    )
    parser.add_argument(
        "--bin-width",
        dest="bin_width",
        help="Gaze digitize bin width, check dataset for more info",
        default=3,
        type=int,
    )
    parser.add_argument(
        "--angle",
        dest="angle",
        help="This is the angle threshold for pitch/yaw, like 42 in our case.",
        default=42,
        type=int,
    )
    parser.add_argument(
        "--patience",
        dest="patience",
        help="Patience for early stopping",
        default=10,
        type=int
    )
    parser.add_argument(
        "--expermient_details",
        dest="expermient_details",
        help="details of the experiment",
        default="",
        type=str
    )
    parser.add_argument(
        "--step_size",
        dest="step_size",
        help="step-size for lr-scheduler",
        default=10,
        type=int
    )
    # ---------------------------------------------------------------------------------------------------------------------
    # Important args ------------------------------------------------------------------------------------------------------
    args = parser.parse_args()
    return args


def load_filtered_state_dict(model, snapshot):
    # By user apaszke from discuss.pytorch.org
    model_dict = model.state_dict()
    snapshot = {k: v for k, v in snapshot.items() if k in model_dict}
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)


def getArch_weights(arch, bins):
    if arch == "MobileNetV4":
        model = MOBILEV4_ADA(num_bins= bins)
        print("MobileNetV4 model is used")
    elif arch == "MobileNetV4_v2":
        model = MOBILEV4_ADA_v2(num_bins= bins)
        print("MobileNetV4_v2 model is used")
    else:
        model = MOBILEV4_ADA_v3(num_bins= bins)
        print("MobileNetV4_v3 model is used")

    return model


def spherical2cartesian(x):
    output = torch.zeros(x.size(0), 3)
    output[:, 2] = -torch.cos(x[:, 1]) * torch.cos(x[:, 0])
    output[:, 0] = torch.cos(x[:, 1]) * torch.sin(x[:, 0])
    output[:, 1] = torch.sin(x[:, 1])
    return output


def compute_angular_error(input, target):
    input = spherical2cartesian(input)
    target = spherical2cartesian(target)
    input = input.view(-1, 3, 1)
    target = target.view(-1, 1, 3)
    output_dot = torch.bmm(target, input)
    output_dot = output_dot.view(-1)
    output_dot = torch.acos(output_dot)
    output_dot = output_dot.data
    output_dot = 180 * torch.mean(output_dot) / math.pi
    return output_dot


if __name__ == "__main__":
    args = parse_args()
    cudnn.enabled = True
    num_epochs = args.num_epochs
    batch_size = args.batch_size
    # gpu = select_device(args.gpu_id, batch_size=args.batch_size)
    gpu = "cuda:" + args.gpu_id
    device = gpu
    data_set = args.dataset
    alpha = args.alpha
    output = args.output
    angle = args.angle
    bin_width = args.bin_width
    writer = SummaryWriter(logdir=output)

    transformations = transforms.Compose(
        [
            transforms.Resize(448),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ]
    )

    model = getArch_weights(args.arch, 90)
    # if args.snapshot == "":
    #     load_filtered_state_dict(model, model_zoo.load_url(pre_url))
    # else:
    #     saved_state_dict = torch.load(args.snapshot)
    #     model.load_state_dict(saved_state_dict)
    
    model.cuda(gpu)
    train_dataset = Gaze360(
        args.gaze360label_dir, args.gaze360image_dir, transformations, 180, 4
    )
    val_dataset = Gaze360(
        args.gaze360vallabel_dir, args.gaze360image_dir, transformations, 180, 4, train=False
    )
    print("Loading data.")
    train_loader_gaze = DataLoader(
        dataset=train_dataset,
        batch_size=int(batch_size),
        shuffle=True,
        num_workers=8,
        pin_memory=True,
    )
    
    val_loader_gaze = DataLoader(
        dataset=val_dataset,
        batch_size=int(batch_size),
        shuffle=False,
        num_workers=8,
        pin_memory=True,
    )
    
    torch.backends.cudnn.benchmark = True

    summary_name = "{}_{}".format("L2CS-gaze360-mobilenetv4-ada", int(time.time()))
    output = os.path.join(output, summary_name)
    os.makedirs(output, exist_ok=True)

    summary_path = os.path.join(output, "best_model")
    os.makedirs(summary_path, exist_ok=True)

    criterion = nn.CrossEntropyLoss().cuda(gpu)
    reg_criterion = nn.MSELoss().cuda(gpu)
    softmax = nn.Softmax(dim=1).cuda(gpu)
    idx_tensor = [idx for idx in range(90)]
    idx_tensor = Variable(torch.FloatTensor(idx_tensor), requires_grad=True).cuda(gpu)

    # # Optimizer gaze
    # optimizer_gaze = torch.optim.Adam(
    #     model.parameters(),
    #     args.lr,
    # )

    # Set the requires_grad attribute for the first group to False directly in the model
    for name, param in model.named_parameters():
        if 'backbone.conv_stem.weight' in name or 'backbone.bn1.weight' in name or 'backbone.bn1.bias' in name:
            param.requires_grad = False

    # Initialize the optimizer with parameter groups
    param_groups = [
        {'params': [], 'lr': args.lr * 0.1},  # Group 1
        {'params': [], 'lr': args.lr * 0.2},  # Group 2
        {'params': [], 'lr': args.lr * 0.6},  # Group 3
        {'params': [], 'lr': args.lr * 0.7},  # Group 4
        {'params': [], 'lr': args.lr * 0.8},  # Group 5
        {'params': [], 'lr': args.lr},        # Group 6
    ]

    # Assign parameters to each group based on your structure
    for name, param in model.named_parameters():
        if param.requires_grad:
            if 'backbone.blocks.0' in name:
                param_groups[0]['params'].append(param)
            elif 'backbone.blocks.1' in name:
                param_groups[1]['params'].append(param)
            elif 'backbone.blocks.2' in name:
                param_groups[2]['params'].append(param)
            elif 'backbone.blocks.3' in name:
                param_groups[3]['params'].append(param)
            elif 'backbone.blocks.4' in name:
                param_groups[4]['params'].append(param)
            elif ('backbone.conv_head' in name or 'backbone.norm_head' in name or
                'adaface_' in name or 'fc_' in name):
                param_groups[5]['params'].append(param)

    # Create the optimizer with the parameter groups
    optimizer_gaze = torch.optim.Adam(param_groups)
    scheduler = torch.optim.lr_scheduler.StepLR(optimizer_gaze, step_size=args.step_size, gamma=0.1)
    
    num_params = sum([np.prod(p.size()) for p in model.parameters()])
    print("Model parameters in millions: ", num_params / 1e6)

    best_loss = float('inf')
    patience_counter = 0
    best_model_weights = copy.deepcopy(model.state_dict())

    configuration = f"\ntrain configuration, gpu_id={args.gpu_id}, batch_size={batch_size}, model_arch={args.arch}\nStart testing dataset={data_set}, loader={len(train_loader_gaze)}------------------------- \n"
    print(configuration)
    
    log_data = []
    
    for epoch in range(num_epochs):
        model.train()  # Ensure the model is in training mode
        sum_loss_pitch_gaze = sum_loss_yaw_gaze = iter_gaze = 0
        
        # Calculate the weight for AdaFace loss and normal loss
        aface_weight = max(0, 1 - epoch / num_epochs)
        normal_weight = min(1, epoch / num_epochs)

        for i, (images_gaze, labels_gaze, cont_labels_gaze, name) in enumerate(
            train_loader_gaze
        ):
            images_gaze = Variable(images_gaze).cuda(gpu)

            # Binned labels
            label_pitch_gaze = Variable(labels_gaze[:, 0]).cuda(gpu)
            label_yaw_gaze = Variable(labels_gaze[:, 1]).cuda(gpu)

            # Continuous labels
            label_pitch_cont_gaze = Variable(cont_labels_gaze[:, 0]).cuda(gpu)
            label_yaw_cont_gaze = Variable(cont_labels_gaze[:, 1]).cuda(gpu)

            # ada_pitch, ada_yaw, pitch, yaw = model(images_gaze, labels_gaze.cuda(gpu), epoch = epoch)   For v2
            
            ada_pitch, ada_yaw, pitch, yaw = model(images_gaze, labels_gaze.cuda(gpu))

            # Cross entropy loss
            loss_pitch_gaze = criterion(pitch, label_pitch_gaze)
            loss_yaw_gaze = criterion(yaw, label_yaw_gaze)

            # MSE loss
            pitch_predicted = softmax(pitch)
            yaw_predicted = softmax(yaw)

            pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * 4 - 180
            yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * 4 - 180

            loss_reg_pitch = reg_criterion(pitch_predicted, label_pitch_cont_gaze)
            loss_reg_yaw = reg_criterion(yaw_predicted, label_yaw_cont_gaze)

            loss_pitch_gaze += alpha * loss_reg_pitch
            loss_yaw_gaze += alpha * loss_reg_yaw

            # AdaFace loss
            ada_loss_pitch_gaze = criterion(ada_pitch, label_pitch_gaze)
            ada_loss_yaw_gaze = criterion(ada_yaw, label_yaw_gaze)
            
            # MSE loss for regression
            ada_pitch_predicted = softmax(ada_pitch)
            ada_yaw_predicted = softmax(ada_yaw)

            ada_pitch_predicted = torch.sum(ada_pitch_predicted * idx_tensor, 1) * bin_width - angle
            ada_yaw_predicted = torch.sum(ada_yaw_predicted * idx_tensor, 1) * bin_width - angle

            ada_loss_reg_pitch = reg_criterion(ada_pitch_predicted, label_pitch_cont_gaze)
            ada_loss_reg_yaw = reg_criterion(ada_yaw_predicted, label_yaw_cont_gaze)

            ada_loss_pitch_gaze += alpha * ada_loss_reg_pitch
            ada_loss_yaw_gaze += alpha * ada_loss_reg_yaw

            # Combined loss with weighted AdaFace and normal loss
            loss_pitch = aface_weight * ada_loss_pitch_gaze + normal_weight * loss_pitch_gaze
            loss_yaw = aface_weight * ada_loss_yaw_gaze + normal_weight * loss_yaw_gaze

            sum_loss_pitch_gaze += loss_pitch
            sum_loss_yaw_gaze += loss_yaw

            loss_seq = [loss_pitch, loss_yaw]
            grad_seq = [torch.tensor(1.0).cuda(gpu) for _ in range(len(loss_seq))]

            optimizer_gaze.zero_grad(set_to_none=True)
            torch.autograd.backward(loss_seq, grad_seq)
            optimizer_gaze.step()

            iter_gaze += 1

            if (i + 1) % 100 == 0:
                print(
                    "Epoch [%d/%d], Iter [%d/%d] Losses: "
                    "Gaze Yaw %.4f,Gaze Pitch %.4f "
                    "Learning rate: %.8f"
                    % (
                        epoch + 1,
                        num_epochs,
                        i + 1,
                        len(train_dataset) // batch_size,
                        sum_loss_pitch_gaze / iter_gaze,
                        sum_loss_yaw_gaze / iter_gaze,
                        optimizer_gaze.param_groups[5]["lr"],
                    )
                )
    

        scheduler.step()  # Update the learning rate
        
        # if epoch % 2 == 0 and epoch < num_epochs:
        #     print(
        #         "Taking snapshot...",
        #         torch.save(
        #             model.state_dict(),
        #             output + "/" + "_epoch_" + str(epoch + 1) + ".pt",
        #         ),
        #     )
        
        # Validation
        model.eval()
        val_loss_pitch_gaze = 0
        val_loss_yaw_gaze = 0
        val_iter_gaze = 0
        total_angle_error = 0.0
        total_samples = 0
        val_loss_delta = 0.01
        with torch.no_grad():
            for (images_gaze, labels_gaze, cont_labels_gaze, name,) in val_loader_gaze:
                # pitch, yaw = model(images_gaze.to(device), training=False, epoch = epoch)         For v2
                
                pitch, yaw = model(images_gaze.to(device), training=False)
 
                total_samples += cont_labels_gaze.size(0)
                loss_pitch_gaze = criterion(pitch, labels_gaze[:, 0].to(device))
                loss_yaw_gaze = criterion(yaw, labels_gaze[:, 1].to(device))

                pitch_predicted = softmax(pitch)
                yaw_predicted = softmax(yaw)

                pitch_predicted = torch.sum(pitch_predicted * idx_tensor, 1) * bin_width - angle
                yaw_predicted = torch.sum(yaw_predicted * idx_tensor, 1) * bin_width - angle

                label_pitch = cont_labels_gaze[:, 0].float() * np.pi / 180 
                label_yaw = cont_labels_gaze[:, 1].float() * np.pi / 180 

                pitch_predicted_rad = pitch_predicted * np.pi / 180 
                yaw_predicted_rad = yaw_predicted * np.pi / 180 

                loss_reg_pitch = reg_criterion(pitch_predicted, cont_labels_gaze[:, 0].to(device))
                loss_reg_yaw = reg_criterion(yaw_predicted, cont_labels_gaze[:, 1].to(device))

                loss_pitch_gaze += alpha * loss_reg_pitch
                loss_yaw_gaze += alpha * loss_reg_yaw

                pitch_predicted_np = pitch_predicted_rad.detach().cpu().numpy()
                yaw_predicted_np = yaw_predicted_rad.detach().cpu().numpy()
                for p, y, pl, yl in zip(pitch_predicted_np, yaw_predicted_np, label_pitch, label_yaw):
                    total_angle_error += angular(gazeto3d([p, y]), gazeto3d([pl, yl]))
                
                val_loss_pitch_gaze += loss_pitch_gaze.item()
                val_loss_yaw_gaze += loss_yaw_gaze.item()
                val_iter_gaze += 1

        avg_val_loss_pitch_gaze = val_loss_pitch_gaze / val_iter_gaze
        avg_val_loss_yaw_gaze = val_loss_yaw_gaze / val_iter_gaze
        avg_val_loss = (avg_val_loss_pitch_gaze + avg_val_loss_yaw_gaze) / 2
        mean_angular_error = total_angle_error / total_samples

        print(
            "Validation - Epoch [%d/%d] Losses: "
            "Gaze Yaw %.4f, Gaze Pitch %.4f, Average loss: %.4f, Mean Angular Error: %.4f"
            % (
                epoch + 1,
                num_epochs,
                avg_val_loss_yaw_gaze,
                avg_val_loss_pitch_gaze,
                avg_val_loss,
                mean_angular_error,
            )
        )
        
        log_data.append({
            'epoch': epoch + 1,
            'train_loss_pitch_gaze': sum_loss_pitch_gaze / iter_gaze,
            'train_loss_yaw_gaze': sum_loss_yaw_gaze / iter_gaze,
            'val_loss_pitch_gaze': avg_val_loss_pitch_gaze,
            'val_loss_yaw_gaze': avg_val_loss_yaw_gaze,
            'val_loss_avg': avg_val_loss,
            'mean_angular_error': mean_angular_error,
            'lr': optimizer_gaze.param_groups[5]['lr'],
            'model size': num_params / 1e6
            
        })
        
        if avg_val_loss < (best_loss):
            best_loss = avg_val_loss
            torch.save(model.state_dict(), os.path.join(summary_path, "best_model.pt"))
            
            
        # writer.add_scalars('Loss', {
        #     'Train/Pitch': sum_loss_pitch_gaze / iter_gaze,
        #     'Train/Yaw': sum_loss_yaw_gaze / iter_gaze,
        #     'Validation/Pitch': avg_val_loss_pitch_gaze,
        #     'Validation/Yaw': avg_val_loss_yaw_gaze
        # }, epoch + 1)
        # writer.add_scalar('Learning Rate', optimizer_gaze.param_groups[0]['lr'], epoch + 1)
        # writer.add_scalar('Mean Angular Error', mean_angular_error, epoch + 1)

        # if avg_val_loss < (best_loss - val_loss_delta):
        #     best_loss = avg_val_loss
        #     patience_counter = 0
        #     best_model_weights = copy.deepcopy(model.state_dict())
        #     torch.save(best_model_weights, os.path.join(summary_path, "best_model.pt"))
        # else:
        #     patience_counter += 1

        # if patience_counter >= args.patience:
        #     print(f"Early stopping at epoch {epoch + 1}")
        #     break 

    torch.save(model.state_dict(), os.path.join(summary_path, "last_model.pt"))

    log_df = pd.DataFrame(log_data)
    log_df.to_csv(os.path.join(summary_path, 'training_log.csv'), index=False)

    config = vars(args)
    with open(os.path.join(summary_path, 'config.json'), 'w') as f:
        json.dump(config, f, indent=4)

writer.close()
        
        
'''
Training

python train_gaze360_mobilenet_ada.py  --dataset gaze360  --gpu 3  --num_epochs 200  --batch_size 16  --lr 0.00003  --alpha 1 --bin-width 4 --angle 180 --arch MobileNetV4 --step_size 75

'''

