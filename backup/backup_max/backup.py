def backup():
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.<PERSON><PERSON><PERSON>, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    # =============================================================================================
    # import torchvision.models as models
    
    # class GazeRegNet(nn.Module):
    #     def __init__(self, num_bins=20):
    #         super().__init__()
    #         # Load pretrained RegNet model
    #         backbone = models.get_model(name="regnet_x_800mf", weights=True)
            
    #         # Save only the feature extractor
    #         self.backbone = nn.Sequential(
    #             backbone.stem,         # stem
    #             backbone.trunk_output, # conv blocks
    #             backbone.avgpool       # AdaptiveAvgPool2d(output_size=(1, 1))
    #         )

    #         self.flatten = nn.Flatten()

    #         # RegNet_x_800mf outputs 672-dim features after avgpool
    #         self.gaze = nn.Linear(672, 2)
            
    #         self._init_weights()

    #     def _init_weights(self):
    #         """Initialize weights for the regression layers."""
    #         # Initialize the regression layer
    #         torch.nn.init.normal_(self.gaze.weight, std=0.001)
    #         if self.gaze.bias is not None:
    #             torch.nn.init.constant_(self.gaze.bias, 0)

    #     def forward(self, x):
    #         x = self.backbone(x)   # (B, 672, 1, 1)
    #         x = self.flatten(x)    # (B, 672)
    #         gaze = self.gaze(x)   # (B, 20)
    #         return gaze
    # model = GazeRegNet()
    
    # model = models.get_model(name="regnet_x_800mf", weights=True)
    
    # Prepare model
    # model = get_model(args.arch, args.bins, pretrained=True)
    # model = get_model(args.arch, args.bins, pretrained=True)

    # util.freeze_batchnorm_layers(model)

    # Freeze stem part via param.requires_grad
    # for param in util.get_params(model, part="stem", require_grad_only=False):
    #     param.requires_grad = False
    
    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="stem", require_grad_only=True), 'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="layer1", require_grad_only=True), 'lr': args.lr*0.7},
    #     {'params': util.get_params(model, part="layer2", require_grad_only=True), 'lr': args.lr*0.8},
    #     {'params': util.get_params(model, part="layer3", require_grad_only=True), 'lr': args.lr*0.9},
    #     {'params': util.get_params(model, part="layer4", require_grad_only=True), 'lr': args.lr*0.9},
    #     # {'params': util.get_params(model, part="backbone", require_grad_only=True), 'lr': args.lr},
    #     {'params': util.get_params(model, part="head", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))

    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="cnn_face", require_grad_only=True),     'lr': args.lr},
    #     # {'params': util.get_params(model, part="cnn_eye", require_grad_only=True),      'lr': args.lr*0.1},
    #     # {'params': util.get_params(model, part="cnn_eye2fc", require_grad_only=True),   'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="fc_face", require_grad_only=True),      'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eye", require_grad_only=True),       'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eyes_face", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))
    
    # Angular Vector Error: 3.9360886° with *0.1
    # optimizer = torch.optim.AdamW(
    #     model.parameters(), 
    #     lr=args.lr, 
    #     weight_decay=1e-2, 
    #     betas=(0.9, 0.999), 
    #     eps=1e-8
    # )
    
    # scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    #     optimizer,
    #     mode='min',             # or 'max' if tracking accuracy
    #     factor=0.1,             # LR is reduced by this factor
    #     patience=5,             # Wait this many epochs with no improvement
    #     threshold=1e-4,         # Minimal change to be considered an improvement
    #     cooldown=0,             # Number of epochs to wait after LR reduction
    #     min_lr=1e-7,            # Minimum LR allowed
    # )
    # scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs)
    # scheduler = torch.optim.lr_scheduler.ExponentialLR(optimizer, gamma=0.9)
    # scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)
    # =============================================================================================
    return