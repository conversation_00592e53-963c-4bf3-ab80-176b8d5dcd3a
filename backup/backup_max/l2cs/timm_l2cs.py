import torch
import torch.nn as nn
from einops import rearrange, repeat
import timm

class L2CS_ViT(nn.Module):
    def __init__(self, image_size, patch_size, num_classes, dim, depth, heads, mlp_dim, channels=3, dropout=0.1):
        super(L2CS_ViT, self).__init__()
        assert image_size % patch_size == 0, "Image dimensions must be divisible by the patch size."
        
        self.image_size = image_size
        self.patch_size = patch_size
        self.num_classes = num_classes
        
        self.backbone = timm.create_model('vit_base_patch16_384', pretrained=True)
        
        self.fc_yaw_gaze = nn.Linear(self.backbone.head.in_features, num_classes)
        self.fc_pitch_gaze = nn.Linear(self.backbone.head.in_features, num_classes)
        
    def forward(self, x):
        x = self.backbone.patch_embed(x)
        cls_tokens = repeat(self.backbone.cls_token, "() n d -> b n d", b=x.size(0))
        x = torch.cat((cls_tokens, x), dim=1)
        x = x + self.backbone.pos_embed[:, :(x.size(1))]
        x = self.backbone.pos_drop(x)
        
        for blk in self.backbone.blocks:
            x = blk(x)
        
        x = self.backbone.norm(x)
        x = x[:, 0]  # Class token output
        
        pre_yaw_gaze = self.fc_yaw_gaze(x)
        pre_pitch_gaze = self.fc_pitch_gaze(x)
        
        return pre_yaw_gaze, pre_pitch_gaze

# Example usage
image_size = 416
patch_size = 16
num_classes = 10  # Number of bins for gaze estimation
dim = 768  # This is fixed for vit_base_patch16_384
depth = 12  # This is fixed for vit_base_patch16_384
heads = 12  # This is fixed for vit_base_patch16_384
mlp_dim = 3072  # This is fixed for vit_base_patch16_384

model = L2CS_ViT(image_size, patch_size, num_classes, dim, depth, heads, mlp_dim)

# Generate a dummy input tensor with shape (batch_size, channels, height, width)
dummy_input = torch.randn(1, 3, image_size, image_size)

# Pass the dummy input through the model
output_yaw, output_pitch = model(dummy_input)

# Print the outputs
print("Output Yaw:", output_yaw)
print("Output Pitch:", output_pitch)
