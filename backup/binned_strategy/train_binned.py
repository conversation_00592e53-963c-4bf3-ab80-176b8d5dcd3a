# === Standard Libraries ===
import os
import logging
import argparse
from tqdm import tqdm

# === Third-Party Libraries ===
import torch
import torch.nn as nn
import torch.nn.functional as F

# === Local Modules ===
from utils import util
from utils.datasets_binned import get_dataloaders
from utils.helpers import get_model, calculate_gaze_angle_error
from config import data_config
from models.vgg_face_binned import FinalModel


util.setup_multi_processes()
util.init_deterministic_seed()


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--data",           type=str,   default="data/dataset_normalized_merged_v1-v2-v3_SCALE_invSR")
    parser.add_argument("--data-config",    type=str,   default="Delta-X-Scaled")
    parser.add_argument("--save-dir",       type=str,   default="runs")
    parser.add_argument("--weights",        type=str,   default="runs/exp_?/best_model.pt")
    parser.add_argument("--num-epochs",     type=int,   default=100)
    parser.add_argument("--batch-size",     type=int,   default=32)
    parser.add_argument("--lr",             type=float, default=0.001)
    parser.add_argument("--arch",           type=str,   default="?")
    parser.add_argument("--alpha",          type=float, default=0.0)
    parser.add_argument("--beta",           type=float, default=1.0)
    parser.add_argument("--gamma",          type=float, default=0.0)
    parser.add_argument("--num-workers",    type=int,   default=8)
    parser.add_argument("--note",           type=str,   default="?")
    parser.add_argument("--train",  action="store_true")
    parser.add_argument("--test",   action="store_true")
    args = parser.parse_args()
    
    # Override default values based on selected dataset
    if args.data_config in data_config:
        dataset_config = data_config[args.data_config]
        args.bins = dataset_config["bins"]
        args.binwidth = dataset_config["binwidth"]
        args.angle = dataset_config["angle"]
    else:
        raise ValueError(f"Unknown dataset: {args.data_config}. Available options: {list(data_config.keys())}")

    return args


def initialize_model(args, device):
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    # Prepare model
    model = FinalModel(args.bins)
    model_dict = model.state_dict()

    author_subject_weights = "runs/exp_120/best_model.pt"
    # author_subject_weights = "weights/author_subject-independent.ckpt"
    snapshot = torch.load(author_subject_weights, map_location=device, weights_only=True)
    
    snapshot = {k: v for k, v in snapshot.items() if k in model_dict and v.shape == model_dict[k].shape}
    # snapshot = {k: v for k, v in snapshot["state_dict"].items() if k in model_dict and v.shape == model_dict[k].shape}
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)
    # exit()
    # Freeze stem part via param.requires_grad
    # for param in util.get_params(model, part="cnn_face", require_grad_only=False):
    #     param.requires_grad = False

    # for param in util.get_params(model, part="fc_face", require_grad_only=False):
    #     param.requires_grad = False
    
    # # Optimizer
    optimizer = torch.optim.Adam([
        {'params': util.get_params(model, part="cnn_face", require_grad_only=False), 'lr': args.lr},
        {'params': util.get_params(model, part="fc_face", require_grad_only=True), 'lr': args.lr},
        {'params': util.get_params(model, part="fc_yaw", require_grad_only=True), 'lr': args.lr},
        {'params': util.get_params(model, part="fc_pitch", require_grad_only=True), 'lr': args.lr},
    ], lr=args.lr) # , weight_decay=5e-4
    
    # optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs)
    
    return model.to(device), optimizer, scheduler


def train_one_epoch(
    args,
    model,
    data_loader,
    cls_criterion,
    reg_criterion,
    optimizer,
    idx_tensor,
    device,
):
    """
    Train the model for one epoch.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model.
        data_loader (DataLoader): DataLoader for the training dataset.
        cls_criterion (nn.Module): Loss function for classification.
        reg_criterion (nn.Module): Loss function for regression.
        optimizer (torch.optim.Optimizer): Optimizer for the model.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform training on.

    Returns:
        Tuple[float, float]: Average losses for pitch and yaw.
    """

    model.train()
    num_batches = len(data_loader)
    sum_angular_error = 0
    total_num_samples = 0
    sum_loss, sum_loss_pitch, sum_loss_yaw = 0, 0, 0
        
    for images, binned_labels, regression_labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        binned_labels = binned_labels.to(device)
        regression_labels = regression_labels.to(device)
        
        # Binned labels
        binned_label_pitch = binned_labels[:, 0]
        binned_label_yaw = binned_labels[:, 1]

        # Regression labels (degrees)
        regression_label_pitch = regression_labels[:, 0]
        regression_label_yaw = regression_labels[:, 1]

        # Forward pass
        binned_pitch, binned_yaw = model(images)

        # Cross Entropy Loss
        loss_binned_pitch = cls_criterion(binned_pitch, binned_label_pitch)
        loss_binned_yaw = cls_criterion(binned_yaw, binned_label_yaw)

        # Softmax
        binned_pitch = F.softmax(binned_pitch, dim=1)
        binned_yaw = F.softmax(binned_yaw, dim=1)

        # Mapping from binned (0 to 90) to angles (-180° to 180°) or (0 to 28) to angles (-42°, 42°)
        pitch = torch.sum(binned_pitch * idx_tensor, 1) * args.binwidth - args.angle
        yaw = torch.sum(binned_yaw * idx_tensor, 1) * args.binwidth - args.angle

        # Mean Squared Error Loss
        loss_regression_pitch = reg_criterion(pitch, regression_label_pitch)
        loss_regression_yaw = reg_criterion(yaw, regression_label_yaw)

        # Calculate binned loss with regression loss
        loss_pitch = (args.alpha * loss_binned_pitch) + (args.beta * loss_regression_pitch)
        loss_yaw = (args.alpha * loss_binned_yaw) + (args.beta * loss_regression_yaw)

        # Convert to radians
        regression_outputs_deg = torch.stack([pitch, yaw], dim=1)
        regression_outputs_rad = torch.deg2rad(regression_outputs_deg)
        regression_labels_rad = torch.deg2rad(regression_labels)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(regression_labels_rad, regression_outputs_rad)
        
        mse_loss_pitch = reg_criterion(regression_labels_rad[:, 0], regression_outputs_rad[:, 0])
        mse_loss_yaw = reg_criterion(regression_labels_rad[:, 1], regression_outputs_rad[:, 1])
        
        loss_pitch += args.alpha * mse_loss_pitch
        loss_yaw += args.alpha * mse_loss_yaw
        
        # ==================================================================
        loss_seq = [loss_pitch, loss_yaw]
        grad_seq = [
            torch.tensor(1.0, device=device),   # loss_pitch
            torch.tensor(1.0, device=device),   # loss_yaw
        ]

        optimizer.zero_grad()
        torch.autograd.backward(loss_seq, grad_seq)
        optimizer.step()
        # ==================================================================
        
        # Store loss and angular error
        num_samples = images.size(0)
        total_num_samples += num_samples
        
        sum_loss_yaw += loss_yaw.item() * num_samples
        sum_loss_pitch += loss_pitch.item() * num_samples
        sum_loss += (loss_pitch + loss_yaw).item() * num_samples
        sum_angular_error += angular_error.item() * num_samples
        
    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_loss_yaw = sum_loss_yaw / total_num_samples
    avg_loss_pitch = sum_loss_pitch / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    return avg_loss, avg_angular_error, avg_loss_pitch, avg_loss_yaw


@torch.no_grad()
def evaluate(
    args,
    model,
    data_loader,
    cls_criterion,
    reg_criterion,
    idx_tensor,
    device,
    mode="val",
):
    """
    Evaluate the model on the test dataset.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform evaluation on.
        mode (str): Evaluation mode ("val" or "test").
    """
    model.eval()
    num_batches = len(data_loader)
    sum_angular_error = 0
    total_num_samples = 0
    sum_loss, sum_loss_pitch, sum_loss_yaw = 0, 0, 0

    for images, binned_labels, regression_labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        binned_labels = binned_labels.to(device)
        regression_labels = regression_labels.to(device)
        
        # Binned labels
        binned_label_pitch = binned_labels[:, 0]
        binned_label_yaw = binned_labels[:, 1]

        # Regression labels (degrees)
        regression_label_pitch = regression_labels[:, 0]
        regression_label_yaw = regression_labels[:, 1]

        # Forward pass
        binned_pitch, binned_yaw = model(images)

        # Cross Entropy Loss
        loss_binned_pitch = cls_criterion(binned_pitch, binned_label_pitch)
        loss_binned_yaw = cls_criterion(binned_yaw, binned_label_yaw)

        # Softmax
        binned_pitch = F.softmax(binned_pitch, dim=1)
        binned_yaw = F.softmax(binned_yaw, dim=1)

        # Mapping from binned (0 to 90) to angles (-180° to 180°) or (0 to 28) to angles (-42°, 42°)
        pitch = torch.sum(binned_pitch * idx_tensor, 1) * args.binwidth - args.angle
        yaw = torch.sum(binned_yaw * idx_tensor, 1) * args.binwidth - args.angle

        # Mean Squared Error Loss
        loss_regression_pitch = reg_criterion(pitch, regression_label_pitch)
        loss_regression_yaw = reg_criterion(yaw, regression_label_yaw)

        # Calculate binned loss with regression loss
        loss_pitch = (args.alpha * loss_binned_pitch) + (args.beta * loss_regression_pitch)
        loss_yaw = (args.alpha * loss_binned_yaw) + (args.beta * loss_regression_yaw)
        # ==================================================================
        
        # Convert to radians
        regression_outputs_deg = torch.stack([pitch, yaw], dim=1)
        regression_outputs_rad = torch.deg2rad(regression_outputs_deg)
        regression_labels_rad = torch.deg2rad(regression_labels)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(regression_labels_rad, regression_outputs_rad)
        
        mse_loss_pitch = reg_criterion(regression_labels_rad[:, 0], regression_outputs_rad[:, 0])
        mse_loss_yaw = reg_criterion(regression_labels_rad[:, 1], regression_outputs_rad[:, 1])
        
        loss_pitch += args.alpha * mse_loss_pitch
        loss_yaw += args.alpha * mse_loss_yaw
        
        # Store loss and angular error
        num_samples = images.size(0)
        total_num_samples += num_samples
        
        sum_loss_yaw += loss_yaw.item() * num_samples
        sum_loss_pitch += loss_pitch.item() * num_samples
        sum_loss += (loss_pitch + loss_yaw).item() * num_samples
        sum_angular_error += angular_error.item() * num_samples
    
    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_loss_yaw = sum_loss_yaw / total_num_samples
    avg_loss_pitch = sum_loss_pitch / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"\nAngular Vector Error: {avg_angular_error:.7f}°"
        )
    
    return avg_loss, avg_angular_error, avg_loss_pitch, avg_loss_yaw


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    args.save_dir = util.set_experiment_results_output(args, root="runs")
    util.safe_yaml_config_file(args)

    # Initialize model, optimizer, and optionally load a checkpoint
    model, optimizer, scheduler = initialize_model(args, device)

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)
    
    # Loss functions and index tensor
    cls_criterion = nn.CrossEntropyLoss()
    reg_criterion = nn.MSELoss()
    idx_tensor = torch.arange(args.bins, device=device, dtype=torch.float32)

    best_model_path = None
    best_error = float('inf')
    performance_results = []
    
    if args.train:
        for epoch in range(args.num_epochs):
            # if epoch == 3:
            #     for param in util.get_params(model, part="fc_face", require_grad_only=False):
            #         param.requires_grad = True
            # elif epoch == 5:
            #     for param in util.get_params(model, part="cnn_face", require_grad_only=False):
            #         param.requires_grad = True
            
            # Update alpha and beta
            # args.alpha = util.get_alpha_cosine(epoch, args.num_epochs)
            # args.beta = 1.0 - args.alpha
        
            # if epoch == 20:
            #     args.alpha = 0.0
            #     args.beta = 1.0
                
            # Train the model for the current epoch
            train_loss, train_angular_error, train_pitch_loss, train_yaw_loss = train_one_epoch(
                args,
                model,
                train_loader,
                cls_criterion,
                reg_criterion,
                optimizer,
                idx_tensor,
                device
            )
            # Evaluate on validation set for the current fold
            val_loss, val_angular_error, val_pitch_loss, val_yaw_loss = evaluate(
                args,
                model,
                val_loader,
                cls_criterion,
                reg_criterion,
                idx_tensor,
                device,
                mode="val",
            )
            
            # Step the scheduler after each epoch
            current_lrs = " | ".join([f"{group['lr']:.0e}" for group in optimizer.param_groups])
            scheduler.step()
            
            # Save the best model
            if val_loss < best_error:
                best_error = val_loss
                best_model_path = os.path.join(args.save_dir, 'best_model.pt')
                torch.save(model.state_dict(), best_model_path)
                status = "*"
            else:
                status = ""

            # Log the results
            logging.info(
                f'Epoch [{epoch + 1}/{args.num_epochs}] '
                f'Losses:   Yaw {train_yaw_loss:.1f},   Pitch {train_pitch_loss:.1f}  |  '
                f'{train_loss:.1f} / {val_loss:.1f}  |  '
                f'AVE: {train_angular_error:.1f}° / {val_angular_error:.1f}°  |  ' # Angular Vector Error
                f'LR: {current_lrs}  |  {status}'
            )
            
            # Save the results for plotting
            result = (epoch, float(train_loss), float(val_loss), float(train_angular_error), float(val_angular_error))
            performance_results.append(result)
        
            # Plot the results
            util.plot_performance_results_splited(args, performance_results)

    if args.test:
        # Test the best model
        if not args.train:
            best_model_path = args.weights
        
        if os.path.exists(best_model_path):
            logging.info(f'Best model saved at {best_model_path}')
            model.load_state_dict(torch.load(best_model_path, map_location=device, weights_only=True))
        else:
            raise ValueError(f"Model weight not found at {best_model_path}")

        model.to(device)
        logging.info("Start Testing")
        val_loss, val_angular_error, val_pitch_loss, val_yaw_loss = evaluate(
            args, model, test_loader, cls_criterion, reg_criterion, idx_tensor, device, mode="test")
        
        # Save the results for plotting
        result = (epoch+1, float(train_loss), float(val_loss), float(train_angular_error), float(val_angular_error))
        performance_results.append(result)
    
        # Plot the results
        util.plot_performance_results_splited(args, performance_results)

if __name__ == '__main__':
    main()
