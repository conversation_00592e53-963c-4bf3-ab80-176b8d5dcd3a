import os
import time
import logging
import argparse
import numpy as np

from tqdm import tqdm
from enum import Enum

from utils import util
from utils.helpers import calculate_gaze_angle_error
from sklearn.model_selection import GroupKFold

util.setup_multi_processes()
util.init_deterministic_seed()
os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'

import torch
import torch.nn as nn
import torch.nn.functional as F

# from models.vggv2 import FinalModel
from models.vgg_face_binned import FinalModel
from utils.datasets_subject import get_dataloaders

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')


class PitchYaw(Enum):
    PITCH = 'pitch'
    YAW = 'yaw'


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser(description="Gaze estimation training")
    parser.add_argument("--data", type=str, default="data/dataset_normalized_merged_v1-v2-v3", help="Directory path for gaze images.")
    parser.add_argument("--output", type=str, default="output/", help="Path of output models.")
    parser.add_argument("--checkpoint", type=str, default="", help="Path to checkpoint for resuming training.")
    parser.add_argument("--num-epochs", type=int, default=100, help="Maximum number of training epochs.")
    parser.add_argument("--batch-size", type=int, default=32, help="Batch size.")
    parser.add_argument("--arch", type=str, default="vgg16",
                        help="Network architecture, currently available: resnet18/34/50, mobilenetv2, mobileone_s0-s4.")
    parser.add_argument("--lr", type=float, default=0.001, help="Base learning rate.")
    parser.add_argument("--num-workers", type=int, default=8, help="Number of workers for data loading.")
    args = parser.parse_args()

    return args


def initialize_model(args, device):
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    # Prepare model
    # model = get_model(args.arch, args.bins, pretrained=True)
    # model = get_model(args.arch, args.bins, pretrained=True)
    model = FinalModel()
    model_dict = model.state_dict()

    best_model_path = "weights/author_subject-independent.ckpt"
    snapshot = torch.load(best_model_path, map_location=device, weights_only=True)
    
    snapshot = {k: v for k, v in snapshot["state_dict"].items() if k in model_dict and v.shape == model_dict[k].shape} # ligtning
    model_dict.update(snapshot)
    model.load_state_dict(model_dict)
    
    # util.freeze_batchnorm_layers(model)

    # Freeze stem part via param.requires_grad
    # for param in util.get_params(model, part="stem", require_grad_only=False):
    #     param.requires_grad = False


    # Optimizers
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=0.0,
        eps=1e-8,  # Prevent division by zero
        betas=(0.9, 0.999)  # Standard Adam parameters
    )
    
    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="stem", require_grad_only=True), 'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="layer1", require_grad_only=True), 'lr': args.lr*0.7},
    #     {'params': util.get_params(model, part="layer2", require_grad_only=True), 'lr': args.lr*0.8},
    #     {'params': util.get_params(model, part="layer3", require_grad_only=True), 'lr': args.lr*0.9},
    #     {'params': util.get_params(model, part="layer4", require_grad_only=True), 'lr': args.lr*0.9},
    #     # {'params': util.get_params(model, part="backbone", require_grad_only=True), 'lr': args.lr},
    #     {'params': util.get_params(model, part="head", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))

    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="cnn_face", require_grad_only=True),     'lr': args.lr},
    #     # {'params': util.get_params(model, part="cnn_eye", require_grad_only=True),      'lr': args.lr*0.1},
    #     # {'params': util.get_params(model, part="cnn_eye2fc", require_grad_only=True),   'lr': args.lr*0.1},
    #     {'params': util.get_params(model, part="fc_face", require_grad_only=True),      'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eye", require_grad_only=True),       'lr': args.lr},
    #     # {'params': util.get_params(model, part="fc_eyes_face", require_grad_only=True), 'lr': args.lr}
    # ], lr=args.lr, weight_decay=5e-4, eps=1e-8, betas=(0.9, 0.999))
    
    # Angular Vector Error: 3.9360886° with *0.1
    # optimizer = torch.optim.AdamW(
    #     model.parameters(), 
    #     lr=args.lr, 
    #     weight_decay=1e-2, 
    #     betas=(0.9, 0.999), 
    #     eps=1e-8
    # )
    
    # # Optional: Add learning rate scheduler for better convergence
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=10,
        verbose=True,
        min_lr=1e-7
    )
    # scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    #     optimizer,
    #     mode='min',             # or 'max' if tracking accuracy
    #     factor=0.1,             # LR is reduced by this factor
    #     patience=5,             # Wait this many epochs with no improvement
    #     threshold=1e-4,         # Minimal change to be considered an improvement
    #     cooldown=0,             # Number of epochs to wait after LR reduction
    #     min_lr=1e-7,            # Minimum LR allowed
    #     verbose=True            # Logs when LR is reduced
    # )
    # scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs)
    # scheduler = torch.optim.lr_scheduler.ExponentialLR(optimizer, gamma=0.9)
    # scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=10, gamma=0.1)

    start_epoch = 0
    if args.checkpoint:
        checkpoint = torch.load(args.checkpoint, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        # Move optimizer states to device
        for state in optimizer.state.values():
            for k, v in state.items():
                if isinstance(v, torch.Tensor):
                    state[k] = v.to(device)

        start_epoch = checkpoint['epoch']
        logging.info(f'Resumed training from {args.checkpoint}, starting at epoch {start_epoch + 1}')

    return model.to(device), optimizer, scheduler, start_epoch


def train_one_epoch(
    args,
    model,
    reg_criterion,
    optimizer,
    data_loader,
    device,
    epoch
):
    """
    Train the model for one epoch.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model.
        cls_criterion (nn.Module): Loss function for classification.
        reg_criterion (nn.Module): Loss function for regression.
        optimizer (torch.optim.Optimizer): Optimizer for the model.
        data_loader (DataLoader): DataLoader for the training dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform training on.
        epoch (int): The current epoch number.

    Returns:
        Tuple[float, float]: Average losses for pitch and yaw.
    """

    model.train()
    sum_loss = 0
    num_batches = len(data_loader)
    
    for batch in tqdm(data_loader, total=num_batches):
        full_face_image = batch['full_face_image'].float().to(device)
        # right_eye_image = batch['right_eye_image'].float().to(device)
        # left_eye_image  = batch['left_eye_image'].float().to(device)
        
        gaze_pitch = batch['gaze_pitch'].float().to(device)
        gaze_yaw   = batch['gaze_yaw'].float().to(device)
        labels = torch.stack([gaze_pitch, gaze_yaw], dim=1).to(device)

        outputs = model(full_face_image)
        # outputs = model(full_face_image, right_eye_image, left_eye_image)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang = calculate_gaze_angle_error(labels, outputs)[0]
        # loss_ang = angular_loss(labels, outputs)

        # Use L1 loss instead of MSE for better stability
        mse_loss = reg_criterion(labels, outputs)

        # Combine losses with safety check
        loss = (loss_ang) + (0.1 * mse_loss)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        # ==================================================================
        
        sum_loss += loss.item()
    
    avg_loss = sum_loss / num_batches

    return avg_loss


@torch.no_grad()
def evaluate(args, model, data_loader, device, mode="val"):
    """
    Evaluate the model on the test dataset.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        model (nn.Module): The gaze estimation model.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        idx_tensor (torch.Tensor): Tensor representing bin indices.
        device (torch.device): Device to perform evaluation on.
    """
    model.eval()
    total_num_samples = 0
    sum_angular_error = 0
    num_batches = len(data_loader)

    for batch in tqdm(data_loader, total=num_batches):
        full_face_image = batch['full_face_image'].float().to(device)
        # right_eye_image = batch['right_eye_image'].float().to(device)
        # left_eye_image  = batch['left_eye_image'].float().to(device)
        gaze_pitch = batch['gaze_pitch'].float().to(device)
        gaze_yaw   = batch['gaze_yaw'].float().to(device)
        labels = torch.stack([gaze_pitch, gaze_yaw], dim=1).to(device)

        outputs = model(full_face_image)
        # outputs = model(full_face_image, right_eye_image, left_eye_image)
        
        sum_angular_error += calculate_gaze_angle_error(labels, outputs)[1] * labels.size(0)
        total_num_samples += labels.size(0)

    avg_angular_error = sum_angular_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"\nAngular Vector Error: {avg_angular_error:.7f}°"
        )
    
    return avg_angular_error


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    summary_name = f'{args.arch}_{int(time.time())}'
    output = os.path.join(args.output, summary_name)
    if not os.path.exists(output):
        os.makedirs(output)

    # Load dataloaders with configurable augmentation
    train_loader , val_loader, test_loader, full_train_dataset = get_dataloaders(args)
    dataset = full_train_dataset

    reg_criterion = nn.MSELoss()
    
    # Create splits
    k = 8 # number of folds
    groups = dataset.person_ids  # must be aligned with dataset indices
    gkf = GroupKFold(n_splits=k)  # 5 folds: ~2 persons for val per fold
    
    fold_errors = []
    best_avg_error = float('inf')
    
    for fold, (train_idx, val_idx) in enumerate(gkf.split(X=np.zeros(len(dataset)), y=np.zeros(len(dataset)), groups=groups)):
        print(f"\nFold {fold + 1}")
        print(f"Train person IDs: {np.unique(groups[train_idx])}")
        print(f"Val   person IDs: {np.unique(groups[val_idx])}")
        best_fold_error = float('inf')

        # Split data into training and validation sets for this fold
        train_subset = torch.utils.data.Subset(dataset, train_idx)
        val_subset = torch.utils.data.Subset(dataset, val_idx)
        
        # Create data loaders for the subsets
        train_loader = torch.utils.data.DataLoader(train_subset,
                                                   batch_size=args.batch_size,
                                                   shuffle=True,
                                                   num_workers=args.num_workers,
                                                   pin_memory=True,
                                                   worker_init_fn=helper.seed_worker,
                                                   generator=util.g)
        val_loader = torch.utils.data.DataLoader(val_subset,
                                                 batch_size=args.batch_size,
                                                 shuffle=False,
                                                 num_workers=args.num_workers,
                                                 pin_memory=True,
                                                 worker_init_fn=helper.seed_worker,
                                                 generator=util.g)
        
        # Reset model and optimizer for each fold
        model, optimizer, scheduler, start_epoch = initialize_model(args, device)

        # Train the model for the current fold
        for epoch in range(start_epoch, args.num_epochs):
            avg_loss = train_one_epoch(
                args,
                model,
                reg_criterion,
                optimizer,
                train_loader,
                device,
                epoch
            )

            current_lr = optimizer.param_groups[-1]['lr']
            
            # Evaluate on validation set for the current fold
            avg_angular_error = evaluate(args, model, val_loader, device, mode="val")
            
            # Step the scheduler after each epoch
            scheduler.step(avg_angular_error)
            
            # Log the results
            logging.info(
                f'Fold {fold+1} | Epoch [{epoch + 1}/{args.num_epochs}] '
                f'Losses:   Avg {avg_loss:.3f}  |  '
                f'Angular Vector Error: {avg_angular_error:.1f}° |  '
                f'Learning Rate: {current_lr:.0e}° |  '
                f'Status: {"*" if avg_angular_error < best_avg_error else ""}'
            )

            # Save the best model for the fold
            if avg_angular_error < best_avg_error:
                best_avg_error = avg_angular_error
                best_model_path = os.path.join(output, f'best_model_fold_{fold+1}.pt')
                torch.save(model.state_dict(), best_model_path)
                logging.info(f'Best model saved for fold {fold+1} at {best_model_path}')
                
            if avg_angular_error < best_fold_error:
                best_fold_error = avg_angular_error
                
        fold_errors.append(best_fold_error)

    # Calculate average error across all folds
    avg_error_overall = np.mean(fold_errors)
    logging.info(f'Average error across {k} folds: {avg_error_overall:.4f}')
    
    # Test the best model
    model = FinalModel()

    # Load the best model for the fold
    if os.path.exists(best_model_path):
        model.load_state_dict(torch.load(best_model_path, map_location=device, weights_only=True))
    else:
        raise ValueError(f"Model weight not found at {best_model_path}")

    # Move the model to the device
    model.to(device)
    logging.info("Start Testing")
    
    # Load test dataloader
    evaluate(args, model, test_loader, device, mode="test")


if __name__ == '__main__':
    main()
