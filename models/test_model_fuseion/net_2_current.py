import torch
from torch import nn
from torchvision.models import vgg16, VGG16_Weights

    
class FinalModel(nn.Module):
    """
    Multi-input neural network for gaze tracking.

    This model processes face and eye images simultaneously using separate CNN branches
    and combines their features for final gaze prediction (pitch and yaw angles).

    Architecture:
    - Face processing branch: VGG16-based CNN with dilated convolutions
    - Eye processing branch: VGG16-based CNN with Squeeze-and-Excitation attention
    - Feature fusion: Concatenation followed by fully connected layers
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the gaze tracking model.
        """
        super().__init__(*args, **kwargs)

        self.cnn_face = nn.Sequential(
            vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9],
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(11, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.fc_face = nn.Sequential(
            nn.Flatten(),
            nn.Dropout(p=0.5),
            nn.Linear(6 * 6 * 128, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),
        )
        
    def forward(self, full_face: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the gaze tracking model.

        Args:
            full_face: Face images of shape (batch_size, 3, 96, 96)

        Returns:
            Predicted gaze angles (pitch, yaw) of shape (batch_size, 2)
        """
        # Process face image
        x = self.cnn_face(full_face)
        gaze = self.fc_face(x)

        return gaze


if __name__ == '__main__':
    model = FinalModel()

    weights_path = "runs/exp_0/best_model.pt"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model.load_state_dict(torch.load(weights_path, map_location=device, weights_only=True))
    model = model.to(device)
    model.eval()
