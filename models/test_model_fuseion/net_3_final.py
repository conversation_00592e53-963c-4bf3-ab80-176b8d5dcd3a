import torch
from torch import nn
import torch.nn.functional as F
from torchvision.models import vgg16, VGG16_Weights


def _make_pair(value):
    if isinstance(value, int):
        value = (value,) * 2
    return value


def conv_layer(in_channels, out_channels, kernel_size, bias=True):
    """
    Re-write convolution layer for adaptive `padding`.
    """
    kernel_size = _make_pair(kernel_size)
    padding = (int((kernel_size[0] - 1) / 2),
               int((kernel_size[1] - 1) / 2))
    return nn.Conv2d(in_channels,
                     out_channels,
                     kernel_size,
                     padding=padding,
                     bias=bias)


class Conv3XC(nn.Module):
    """
    Structural reparameterization convolution block that fuses multiple convolutions
    into a single 3x3 conv during inference for reduced FLOPs.
    
    Training: skip_connection + bottleneck_conv (1x1 -> 3x3 -> 1x1)
    Inference: single 3x3 conv (fused)
    """
    def __init__(self, c_in, c_out, gain1=1, s=1, bias=True, relu=False):
        super(Conv3XC, self).__init__()
        self.weight_concat = None
        self.bias_concat = None
        self.stride = s
        self.has_relu = relu
        gain = gain1

        # Skip connection (1x1 conv)
        self.sk = nn.Conv2d(in_channels=c_in, out_channels=c_out, 
                           kernel_size=1, padding=0, stride=s, bias=bias)
        
        # Bottleneck convolution sequence (1x1 -> 3x3 -> 1x1)
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels=c_in, out_channels=c_in * gain, 
                     kernel_size=1, padding=0, bias=bias),
            nn.Conv2d(in_channels=c_in * gain, out_channels=c_out * gain, 
                     kernel_size=3, stride=s, padding=0, bias=bias),
            nn.Conv2d(in_channels=c_out * gain, out_channels=c_out, 
                     kernel_size=1, padding=0, bias=bias),
        )

        # Fused convolution for inference
        self.eval_conv = nn.Conv2d(in_channels=c_in, out_channels=c_out, 
                                  kernel_size=3, padding=1, stride=s, bias=bias)
        self.eval_conv.weight.requires_grad = False
        self.eval_conv.bias.requires_grad = False
        self.inference_mode = False

    def update_params(self, remove_unused=False):
        """
        Fuse the skip connection and bottleneck convolutions into a single 3x3 conv.
        This dramatically reduces FLOPs during inference.
        """
        # Extract weights and biases from bottleneck sequence
        w1 = self.conv[0].weight.data.clone().detach()
        b1 = self.conv[0].bias.data.clone().detach()
        w2 = self.conv[1].weight.data.clone().detach()
        b2 = self.conv[1].bias.data.clone().detach()
        w3 = self.conv[2].weight.data.clone().detach()
        b3 = self.conv[2].bias.data.clone().detach()

        # Fuse first two convolutions (1x1 -> 3x3)
        w = F.conv2d(w1.flip(2, 3).permute(1, 0, 2, 3), w2, padding=2, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        b = (w2 * b1.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b2

        # Fuse with third convolution (-> 1x1)
        self.weight_concat = F.conv2d(w.flip(2, 3).permute(1, 0, 2, 3), w3, padding=0, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        self.bias_concat = (w3 * b.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b3

        # Add skip connection (1x1 conv padded to 3x3)
        sk_w = self.sk.weight.data.clone().detach()
        sk_b = self.sk.bias.data.clone().detach()
        target_kernel_size = 3

        H_pixels_to_pad = (target_kernel_size - 1) // 2
        W_pixels_to_pad = (target_kernel_size - 1) // 2
        sk_w = F.pad(sk_w, [H_pixels_to_pad, H_pixels_to_pad, W_pixels_to_pad, W_pixels_to_pad])

        # Final fused weights and biases
        self.weight_concat = self.weight_concat + sk_w
        self.bias_concat = self.bias_concat + sk_b

        # Set fused parameters to eval_conv
        self.eval_conv.weight.data = self.weight_concat
        self.eval_conv.bias.data = self.bias_concat

        if remove_unused:
            self.conv = nn.Identity()
            self.sk = nn.Identity()

    def forward(self, x):
        if self.training:
            # Training mode: use complex structure
            pad = 1
            x_pad = F.pad(x, (pad, pad, pad, pad), "constant", 0)
            out = self.conv(x_pad) + self.sk(x)
        else:
            # Inference mode: use fused single convolution
            if not self.inference_mode:
                self.update_params()
            out = self.eval_conv(x)

        if self.has_relu:
            out = F.leaky_relu(out, negative_slope=0.05)
        return out


class FusedBlock(nn.Module):
    """
    Efficient block using Conv3XC fusion technique with residual connection.
    Replaces traditional conv blocks in the gaze tracking model.
    """
    def __init__(self, in_channels, out_channels, gain=2, use_residual=True):
        super(FusedBlock, self).__init__()
        self.use_residual = use_residual and (in_channels == out_channels)
        
        self.conv1 = Conv3XC(in_channels, out_channels, gain1=gain, relu=True)
        self.conv2 = Conv3XC(out_channels, out_channels, gain1=gain, relu=True)
        self.bn = nn.BatchNorm2d(out_channels)
        
    def forward(self, x):
        identity = x if self.use_residual else None
        
        out = self.conv1(x)
        out = self.conv2(out)
        out = self.bn(out)
        
        if self.use_residual:
            out = out + identity
            
        return out


class FinalModel(nn.Module):
    """
    Efficient gaze tracking model using Conv3XC fusion technique for reduced FLOPs.
    
    This model maintains the same architecture as net_2_current.py but replaces
    regular convolutions with Conv3XC blocks that can be fused during inference
    to dramatically reduce computational cost for TI board deployment.
    
    Architecture:
    - VGG16 backbone (first 9 layers) for feature extraction
    - Conv3XC fusion blocks for efficient processing
    - Dilated convolutions for expanded receptive field
    - Fully connected layers for gaze prediction
    """

    def __init__(self, feature_channels=64, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # VGG16 backbone for initial feature extraction
        # self.backbone = vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9]
        self.backbone = vgg16(weights=None).features[:9]
        
        # Efficient fusion-based feature processing
        self.feature_adapter = Conv3XC(128, feature_channels, gain1=2, relu=True)
        
        # Efficient dilated convolution blocks using fusion technique
        self.dilated_block1 = FusedBlock(feature_channels, feature_channels, gain=2)
        self.dilated_conv1 = nn.Conv2d(feature_channels, feature_channels, kernel_size=3, padding=2, dilation=2)
        
        self.dilated_block2 = FusedBlock(feature_channels, feature_channels, gain=2)
        self.dilated_conv2 = nn.Conv2d(feature_channels, feature_channels, kernel_size=3, padding=3, dilation=3)
        
        self.dilated_block3 = FusedBlock(feature_channels, feature_channels*2, gain=2, use_residual=False)
        self.dilated_conv3 = nn.Conv2d(feature_channels*2, feature_channels*2, kernel_size=3, padding=5, dilation=5)
        
        self.final_fusion = Conv3XC(feature_channels*2, feature_channels*2, gain1=2, relu=True)
        
        # Activation and normalization
        self.relu = nn.ReLU(inplace=True)
        self.bn1 = nn.BatchNorm2d(feature_channels)
        self.bn2 = nn.BatchNorm2d(feature_channels)
        self.bn3 = nn.BatchNorm2d(feature_channels*2)
        
        # Fully connected layers for gaze prediction
        self.fc_layers = nn.Sequential(
            nn.Flatten(),
            nn.Dropout(p=0.5),
            # nn.Linear(6 * 6 * feature_channels*2, 256),
            nn.Linear(294_912, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),  # pitch and yaw
        )#294_912

    def forward(self, full_face: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the efficient gaze tracking model.

        Args:
            full_face: Face images of shape (batch_size, 3, 96, 96)

        Returns:
            Predicted gaze angles (pitch, yaw) of shape (batch_size, 2)
        """
        # VGG16 backbone feature extraction
        x = self.backbone(full_face)
        
        # Adapt features to our channel size
        x = self.feature_adapter(x)
        
        # Efficient dilated processing with fusion blocks
        x = self.dilated_block1(x)
        x = self.dilated_conv1(x)
        x = self.relu(self.bn1(x))
        
        x = self.dilated_block2(x)
        x = self.dilated_conv2(x)
        x = self.relu(self.bn2(x))
        
        x = self.dilated_block3(x)
        x = self.dilated_conv3(x)
        x = self.relu(self.bn3(x))
        
        # Final fusion processing
        x = self.final_fusion(x)
        
        # Gaze prediction
        gaze = self.fc_layers(x)
        
        return gaze
    
    def activate_inference_mode(self):
        """
        Activate inference mode to fuse all Conv3XC blocks into single convolutions.
        Call this method before deployment to TI board for maximum efficiency.
        """
        for module in self.modules():
            if hasattr(module, "update_params"):
                module.update_params(remove_unused=True)
                module.inference_mode = True
        print("Inference mode activated - all Conv3XC blocks fused for maximum efficiency!")


if __name__ == '__main__':
    # Test the model
    model = FinalModel(feature_channels=64)

    # Test with random input
    test_input = torch.randn(1, 3, 96, 96)

    print("Testing model architecture...")
    model.train()

    # Debug: Check intermediate shapes
    x = model.backbone(test_input)
    print(f"After backbone: {x.shape}")

    x = model.feature_adapter(x)
    print(f"After feature_adapter: {x.shape}")

    x = model.dilated_block1(x)
    x = model.dilated_conv1(x)
    x = model.relu(model.bn1(x))
    print(f"After dilated_block1: {x.shape}")

    x = model.dilated_block2(x)
    x = model.dilated_conv2(x)
    x = model.relu(model.bn2(x))
    print(f"After dilated_block2: {x.shape}")

    x = model.dilated_block3(x)
    x = model.dilated_conv3(x)
    x = model.relu(model.bn3(x))
    print(f"After dilated_block3: {x.shape}")

    x = model.final_fusion(x)
    print(f"After final_fusion: {x.shape}")

    x_flat = torch.flatten(x, 1)
    print(f"After flatten: {x_flat.shape}")

    # Calculate correct linear layer input size
    linear_input_size = x_flat.shape[1]
    print(f"Required linear input size: {linear_input_size}")

    print("\nModel architecture analysis complete!")
    print("You need to update the Linear layer input size in the fc_layers.")
