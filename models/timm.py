import timm
import torch


class TimmArchitectures(torch.nn.Module):
    def __init__(self, backbone_name=None, num_classes=2):
        """
        Landmark Detection model using a timm architectures.
        https://github.com/huggingface/pytorch-image-models/blob/main/results/results-imagenet.csv
        
        Args:
            backbone_name (str): Name of timm backbone.
            num_classes (int): Number of classes to predict.
        """
        super(TimmArchitectures, self).__init__()
        
        # Load timm architecture
        self.architecture = self.get_architecture(backbone_name, num_classes)
        
    def forward(self, x):
        """
        Forward pass for the model.
        
        Args:
            x (torch.Tensor): Input image tensor of shape (B, C, H, W).
            
        Returns:
            torch.Tensor: Predicted classes of shape (B, num_classes).
        """
        landmarks = self.architecture(x)
        return landmarks
    
    @staticmethod
    def get_architecture(backbone_name, num_classes, pretrained=True, features_only=False):
        """
        Load the timm architecture with modified head.
        
        Args:
            backbone_name (str): Name of timm backbone.
        
        Returns:
            nn.Sequential: timm architecture.
        """        
        backbone = timm.create_model(
            model_name=backbone_name,
            num_classes=num_classes,
            pretrained=pretrained,
            features_only=features_only,
            )
        return backbone
