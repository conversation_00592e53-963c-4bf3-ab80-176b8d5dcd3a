import torch
from torch import nn, Tensor
from torchvision.models import (
    vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, vgg19_bn,
    VGG11_Weights, VGG11_BN_Weights, VGG13_Weights, VGG13_BN_Weights,
    VGG16_Weights, VGG16_BN_Weights, VGG19_Weights, VGG19_BN_Weights
)

from typing import Any, List, Tuple


__all__ = ["vgg11_gaze", "vgg11_bn_gaze", "vgg13_gaze", "vgg13_bn_gaze",
           "vgg16_gaze", "vgg16_bn_gaze", "vgg19_gaze", "vgg19_bn_gaze"]


# VGG configurations for different architectures
# Each list represents the number of output channels for each layer
# 'M' represents a MaxPool2d layer
VGG_CONFIGS = {
    'vgg11': [64, 'M', 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg13': [64, 64, 'M', 128, 128, 'M', 256, 256, 'M', 512, 512, 'M', 512, 512, 'M'],
    'vgg16': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 512, 512, 512, 'M', 512, 512, 512, 'M'],
    'vgg19': [64, 64, 'M', 128, 128, 'M', 256, 256, 256, 256, 'M', 512, 512, 512, 512, 'M', 512, 512, 512, 512, 'M'],
}


def make_vgg_layers(config: List, batch_norm: bool = False) -> nn.Sequential:
    """
    Create VGG feature layers based on configuration.

    Args:
        config: List defining the architecture (numbers = channels, 'M' = MaxPool)
        batch_norm: Whether to include batch normalization layers

    Returns:
        Sequential module containing the VGG feature layers
    """
    layers = []
    in_channels = 3

    for v in config:
        if v == 'M':
            layers += [nn.MaxPool2d(kernel_size=2, stride=2)]
        else:
            conv2d = nn.Conv2d(in_channels, v, kernel_size=3, padding=1)
            if batch_norm:
                layers += [conv2d, nn.BatchNorm2d(v), nn.ReLU(inplace=True)]
            else:
                layers += [conv2d, nn.ReLU(inplace=True)]
            in_channels = v

    return nn.Sequential(*layers)


class VGGGaze(nn.Module):
    """
    VGG architecture adapted for gaze estimation.

    Similar to ResNet implementation but using VGG backbone for feature extraction.
    Outputs both classification bins and supports eye coordinate prediction.
    """

    def __init__(
        self,
        vgg_name: str = 'vgg16',
        batch_norm: bool = False,
        num_classes: int = 90,
        pretrained: bool = True,
        dropout: float = 0.5
    ) -> None:
        super().__init__()

        self.num_classes = num_classes

        # Create VGG feature extractor
        if vgg_name not in VGG_CONFIGS:
            raise ValueError(f"Unknown VGG configuration: {vgg_name}")

        self.features = make_vgg_layers(VGG_CONFIGS[vgg_name], batch_norm)

        # Adaptive pooling to handle different input sizes
        self.avgpool = nn.AdaptiveAvgPool2d((7, 7))

        # Calculate the number of features after adaptive pooling
        # VGG typically ends with 512 channels, and 7x7 = 49
        feature_size = 512 * 7 * 7

        # Classifier layers similar to original VGG
        self.classifier = nn.Sequential(
            nn.Linear(feature_size, 4096),
            nn.ReLU(True),
            nn.Dropout(p=dropout),
            nn.Linear(4096, 4096),
            nn.ReLU(True),
            nn.Dropout(p=dropout),
        )

        # Gaze prediction heads (similar to ResNet implementation)
        self.fc_yaw = nn.Linear(4096, num_classes)
        self.fc_pitch = nn.Linear(4096, num_classes)

        # Initialize weights
        self._initialize_weights()

        # Load pretrained weights if requested
        if pretrained:
            self._load_pretrained_weights(vgg_name, batch_norm)

    def _initialize_weights(self) -> None:
        """Initialize weights for the network."""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                nn.init.constant_(m.bias, 0)


    def _load_pretrained_weights(self, vgg_name: str, batch_norm: bool) -> None:
        """Load pretrained VGG weights from torchvision."""
        try:
            # Map our VGG names to torchvision functions and weights
            vgg_mapping = {
                ('vgg11', False): (vgg11, VGG11_Weights.DEFAULT),
                ('vgg11', True): (vgg11_bn, VGG11_BN_Weights.DEFAULT),
                ('vgg13', False): (vgg13, VGG13_Weights.DEFAULT),
                ('vgg13', True): (vgg13_bn, VGG13_BN_Weights.DEFAULT),
                ('vgg16', False): (vgg16, VGG16_Weights.DEFAULT),
                ('vgg16', True): (vgg16_bn, VGG16_BN_Weights.DEFAULT),
                ('vgg19', False): (vgg19, VGG19_Weights.DEFAULT),
                ('vgg19', True): (vgg19_bn, VGG19_BN_Weights.DEFAULT),
            }

            if (vgg_name, batch_norm) in vgg_mapping:
                vgg_func, weights = vgg_mapping[(vgg_name, batch_norm)]
                pretrained_model = vgg_func(weights=weights)

                # Load feature extractor weights
                self.features.load_state_dict(pretrained_model.features.state_dict())

                # Load classifier weights (first 4 layers, excluding the final classification layer)
                pretrained_classifier = pretrained_model.classifier
                our_classifier = self.classifier

                # Copy weights for the layers that match
                our_classifier[0].weight.data.copy_(pretrained_classifier[0].weight.data)
                our_classifier[0].bias.data.copy_(pretrained_classifier[0].bias.data)
                our_classifier[3].weight.data.copy_(pretrained_classifier[3].weight.data)
                our_classifier[3].bias.data.copy_(pretrained_classifier[3].bias.data)

                print(f"Loaded pretrained weights for {vgg_name} (batch_norm={batch_norm})")
            else:
                print(f"No pretrained weights available for {vgg_name} (batch_norm={batch_norm})")

        except Exception as e:
            print(f"Failed to load pretrained weights: {e}")

    def forward(self, x: Tensor) -> Tuple[Tensor, Tensor, Tensor, Tensor]:
        """
        Forward pass of the VGG gaze estimation model.

        Args:
            x: Input tensor of shape (batch_size, 3, height, width)

        Returns:
            Tuple of (pitch, yaw) predictions
        """
        # Feature extraction
        x = self.features(x)
        x = self.avgpool(x)
        x = torch.flatten(x, 1)

        # Classifier
        x = self.classifier(x)

        # Gaze predictions
        yaw = self.fc_yaw(x)
        pitch = self.fc_pitch(x)

        return pitch, yaw


# Factory functions for different VGG architectures
def vgg11_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-11 model for gaze estimation."""
    return VGGGaze(vgg_name='vgg11', batch_norm=False, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg11_bn_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-11 model with batch normalization for gaze estimation."""
    return VGGGaze(vgg_name='vgg11', batch_norm=True, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg13_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-13 model for gaze estimation."""
    return VGGGaze(vgg_name='vgg13', batch_norm=False, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg13_bn_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-13 model with batch normalization for gaze estimation."""
    return VGGGaze(vgg_name='vgg13', batch_norm=True, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg16_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-16 model for gaze estimation."""
    return VGGGaze(vgg_name='vgg16', batch_norm=False, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg16_bn_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-16 model with batch normalization for gaze estimation."""
    return VGGGaze(vgg_name='vgg16', batch_norm=True, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg19_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-19 model for gaze estimation."""
    return VGGGaze(vgg_name='vgg19', batch_norm=False, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)


def vgg19_bn_gaze(*, pretrained: bool = True, num_classes: int = 90, **kwargs: Any) -> VGGGaze:
    """VGG-19 model with batch normalization for gaze estimation."""
    return VGGGaze(vgg_name='vgg19', batch_norm=True, num_classes=num_classes,
                   pretrained=pretrained, **kwargs)
