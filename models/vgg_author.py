import torch
from torch import nn
from torchvision.models import vgg16, VGG16_Weights, vgg19_bn, VGG19_BN_Weights


class SELayer(nn.Module):
    """
    Squeeze-and-Excitation layer for channel attention.

    This layer applies channel-wise attention by first squeezing spatial dimensions
    to get global channel statistics, then exciting (re-weighting) channels based
    on their importance.

    Reference: https://github.com/moskomule/senet.pytorch/blob/master/senet/se_module.py

    Args:
        channel: Number of input channels
        reduction: Reduction ratio for the bottleneck layer (default: 16)
    """

    def __init__(self, channel: int, reduction: int = 16):
        super(SELayer, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)  # Squeeze: global average pooling
        self.fc = nn.Sequential(  # Excitation: channel attention mechanism
            nn.Linear(channel, channel // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channel // reduction, channel, bias=False),
            nn.Sig<PERSON>id()
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the Squeeze-and-Excitation layer.

        Args:
            x: Input tensor of shape (batch_size, channels, height, width)

        Returns:
            Output tensor with channel-wise attention applied
        """
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y.expand_as(x)


class FinalModel(nn.Module):
    """
    Multi-input neural network for gaze tracking.

    This model processes face and eye images simultaneously using separate CNN branches
    and combines their features for final gaze prediction (pitch and yaw angles).

    Architecture:
    - Face processing branch: VGG16-based CNN with dilated convolutions
    - Eye processing branch: VGG16-based CNN with Squeeze-and-Excitation attention
    - Feature fusion: Concatenation followed by fully connected layers
    """

    def __init__(self, use_vgg16: bool = True, *args, **kwargs):
        """
        Initialize the gaze tracking model.

        Args:
            use_vgg16: Whether to use VGG16 (True) or VGG19_BN (False) as backbone
        """
        super().__init__(*args, **kwargs)

        # Subject-specific biases (currently disabled)
        # self.subject_biases = nn.Parameter(torch.zeros(15 * 2, 2))

        # if use_vgg16:
        #     # First four convolutional layers of VGG16 pretrained on ImageNet
        #     backbone = vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9]
        #     backbone_face = vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9]
        #     backbone_eye = vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9]
        # else:
        #     # First four convolutional layers of VGG19_BN pretrained on ImageNet
        #     backbone = vgg19_bn(weights=VGG19_BN_Weights.IMAGENET1K_V1).features[:13]

        self.cnn_face = nn.Sequential(
            vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9],
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(11, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.cnn_eye = nn.Sequential(
            vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9],
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(4, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.fc_face = nn.Sequential(
            nn.Flatten(),
            nn.Linear(6 * 6 * 128, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Linear(256, 64),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(64),
        )

        self.cnn_eye2fc = nn.Sequential(
            SELayer(256),

            nn.Conv2d(256, 256, kernel_size=(3, 3), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(256),

            SELayer(256),

            nn.Conv2d(256, 128, kernel_size=(3, 3), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),

            SELayer(128),
        )

        self.fc_eye = nn.Sequential(
            nn.Flatten(),
            nn.Linear(4 * 6 * 128, 512),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(512),
        )

        self.fc_eyes_face = nn.Sequential(
            nn.Dropout(p=0.5),
            nn.Linear(576, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),
        )

    def forward(self,
                full_face: torch.Tensor,
                right_eye: torch.Tensor,
                left_eye: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the gaze tracking model.

        Args:
            person_idx: Person indices for subject-specific biases (currently unused)
            full_face: Face images of shape (batch_size, 3, 96, 96)
            right_eye: Right eye images of shape (batch_size, 3, 64, 96)
            left_eye: Left eye images of shape (batch_size, 3, 64, 96)

        Returns:
            Predicted gaze angles (pitch, yaw) of shape (batch_size, 2)
        """
        # Process face image
        out_cnn_face = self.cnn_face(full_face)
        out_fc_face = self.fc_face(out_cnn_face)

        # Process eye images separately
        out_cnn_right_eye = self.cnn_eye(right_eye)
        out_cnn_left_eye = self.cnn_eye(left_eye)
        out_cnn_eye = torch.cat((out_cnn_right_eye, out_cnn_left_eye), dim=1)

        # Apply attention and feature fusion for eyes
        cnn_eye2fc_out = self.cnn_eye2fc(out_cnn_eye)
        out_fc_eye = self.fc_eye(cnn_eye2fc_out)

        # Combine face and eye features
        fc_concatenated = torch.cat((out_fc_face, out_fc_eye), dim=1)
        gaze_prediction = self.fc_eyes_face(fc_concatenated)

        # Subject-specific biases are currently disabled
        return gaze_prediction


if __name__ == '__main__':
    model = FinalModel()

    # Quick inspect the face‐CNN submodule
    print(model)
