import torch
from torch import nn
from torchvision.models import vgg16, VGG16_Weights

from torchinfo import summary  # pip install torchinfo
from fvcore.nn import FlopCountAnalysis, flop_count_table


class FinalModel(nn.Module):
    """
    Multi-input neural network for gaze tracking.

    This model processes face and eye images simultaneously using separate CNN branches
    and combines their features for final gaze prediction (pitch and yaw angles).

    Architecture:
    - Face processing branch: VGG16-based CNN with dilated convolutions
    - Eye processing branch: VGG16-based CNN with Squeeze-and-Excitation attention
    - Feature fusion: Concatenation followed by fully connected layers
    """

    def __init__(self, *args, **kwargs):
        """
        Initialize the gaze tracking model.
        """
        super().__init__(*args, **kwargs)

        self.cnn_face = nn.Sequential(
            # vgg16(weights=VGG16_Weights.IMAGENET1K_V1).features[:9],
            vgg16(weights=None).features[:9],
            nn.Conv2d(128, 64, kernel_size=(1, 1), stride=(1, 1), padding='same'),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(2, 2)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(3, 3)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(64),
            nn.Conv2d(64, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(5, 5)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
            nn.Conv2d(128, 128, kernel_size=(3, 3), stride=(1, 1), padding='valid', dilation=(11, 11)),
            nn.ReLU(inplace=True),
            nn.BatchNorm2d(128),
        )

        self.fc_face = nn.Sequential(
            nn.Flatten(),
            nn.Dropout(p=0.5),
            nn.Linear(6 * 6 * 128, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),
        )
        
    def forward(self, full_face: torch.Tensor) -> torch.Tensor:
        """
        Forward pass of the gaze tracking model.

        Args:
            full_face: Face images of shape (batch_size, 3, 96, 96)

        Returns:
            Predicted gaze angles (pitch, yaw) of shape (batch_size, 2)
        """
        # Process face image
        x = self.cnn_face(full_face)
        gaze = self.fc_face(x)

        return gaze


if __name__ == '__main__':
    model = FinalModel()

    weights_path = "runs/exp_0/best_model.pt"
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model.load_state_dict(torch.load(weights_path, map_location=device, weights_only=True))
    model = model.to(device)
    model.eval()

    # Dummy input
    size = 96
    inputs = torch.randn(1, 3, size, size, device=device)

    # FLOPs analysis
    flops = FlopCountAnalysis(model, inputs)
    print("==== FLOP Count Table ====")
    print(flop_count_table(flops))

    # Optional: more detailed summary
    print("\n==== torchinfo Summary ====")
    summary(model, input_size=(1, 3, size, size))
    
    print(f"FLOPs: {flops.total():,}\n")
    
    print(f"MFLOPs: {flops.total() / 1e6:.3f}")
    print(f"GFLOPs: {flops.total() / 1e9:.3f}")
    print(f"TFLOPs: {flops.total() / 1e12:.6f}")
    
    TFLOPS_MAX = 4
    max_inferences_per_second = TFLOPS_MAX / (flops.total() / 1e12)
    print(f"≈ {max_inferences_per_second:.0f} inferences per second on TI board")

    # print(model)

""" Default
==========================================================================================
Total params: 1,745,538
Trainable params: 1,745,538
Non-trainable params: 0
Total mult-adds (Units.GIGABYTES): 1.08
==========================================================================================
Input size (MB): 0.11
Forward/backward pass size (MB): 21.66
Params size (MB): 6.98
Estimated Total Size (MB): 28.75
==========================================================================================
FLOPs: 867,389,952

MFLOPs: 867.390
GFLOPs: 0.867
TFLOPs: 0.000867
≈ 4612 inferences per second on TI board
"""
