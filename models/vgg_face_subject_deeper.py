import torch
from torch import nn
from torch.nn import functional as F


class Conv3XC(nn.Module):
    """
    Fusable 3x3 Conv built from (1x1 -> 3x3 -> 1x1) with a parallel 1x1 skip.
    During training it runs the explicit multi-conv path. For inference, the
    three layers + skip can be re-parameterized (fused) into a single 3x3 conv
    with identical outputs, reducing FLOPs/params at deploy time.

    Call `update_params(remove_unused=True)` once (or `model.active_inference()`)
    before export/deploy to materialize the fused 3x3 weights/bias.
    """

    def __init__(self, c_in, c_out, gain=2, s=1, bias=True, relu=False):
        super().__init__()
        self.stride = s
        self.has_relu = relu

        # Parallel 1x1 skip path (re-parameterizable into 3x3 via zero-padding)
        self.sk = nn.Conv2d(c_in, c_out, kernel_size=1, padding=0, stride=s, bias=bias)

        # 1x1 -> 3x3 (no padding, we pad input explicitly) -> 1x1
        self.conv = nn.Sequential(
            nn.Conv2d(c_in, c_in * gain, kernel_size=1, padding=0, bias=bias),
            nn.Conv2d(c_in * gain, c_out * gain, kernel_size=3, stride=s, padding=0, bias=bias),
            nn.Conv2d(c_out * gain, c_out, kernel_size=1, padding=0, bias=bias),
        )

        # Single 3x3 used for evaluation/inference after fusion
        self.eval_conv = nn.Conv2d(c_in, c_out, kernel_size=3, padding=1, stride=s, bias=bias)
        self.eval_conv.weight.requires_grad = False
        self.eval_conv.bias.requires_grad = False
        self.inference_mode = False

    @torch.no_grad()
    def update_params(self, remove_unused: bool = False):
        """Fuse (1x1 -> 3x3 -> 1x1) + skip(1x1) into a single 3x3.
        Optionally strip the original layers to save memory.
        """
        # Extract weights/biases
        w1, b1 = self.conv[0].weight.data.clone(), self.conv[0].bias.data.clone()
        w2, b2 = self.conv[1].weight.data.clone(), self.conv[1].bias.data.clone()
        w3, b3 = self.conv[2].weight.data.clone(), self.conv[2].bias.data.clone()

        # Fuse first two convs: effective 3x3 in the middle space
        w12 = F.conv2d(w1.flip(2, 3).permute(1, 0, 2, 3), w2, padding=2, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        b12 = (w2 * b1.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b2

        # Fuse with final 1x1
        w123 = F.conv2d(w12.flip(2, 3).permute(1, 0, 2, 3), w3, padding=0, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        b123 = (w3 * b12.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b3

        # Bring in the skip 1x1 as a zero-padded 3x3 and add
        sk_w = self.sk.weight.data.clone()
        sk_b = self.sk.bias.data.clone()
        sk_w = F.pad(sk_w, [1, 1, 1, 1])  # pad to 3x3

        fused_w = w123 + sk_w
        fused_b = b123 + sk_b

        # Materialize
        self.eval_conv.weight.data.copy_(fused_w)
        self.eval_conv.bias.data.copy_(fused_b)

        if remove_unused:
            self.conv = nn.Identity()
            self.sk = nn.Identity()
            self.inference_mode = True

    def forward(self, x):
        if self.training:
            # explicit padding for the middle 3x3
            x_pad = F.pad(x, (1, 1, 1, 1), value=0.0)
            out = self.conv(x_pad) + self.sk(x)
        else:
            if not self.inference_mode:
                # lazily fuse on first eval forward for identical outputs
                self.update_params(remove_unused=False)
            out = self.eval_conv(x)
        
        if self.has_relu:
            out = F.leaky_relu(out, negative_slope=0.05)
        return out


# =============================================================
# Accuracy-oriented variants (residual + attention + learned downsample)
# =============================================================

class ECA(nn.Module):
    """Efficient Channel Attention (cheap, no BN)."""
    def __init__(self, c: int, k: int = 3):
        super().__init__()
        self.conv = nn.Conv1d(1, 1, kernel_size=k, padding=k // 2, bias=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (B,C,H,W)
        y = F.adaptive_avg_pool2d(x, 1)        # (B,C,1,1)
        y = y.squeeze(-1).transpose(1, 2)      # (B,1,C)
        y = torch.sigmoid(self.conv(y))        # (B,1,C)
        y = y.transpose(1, 2).unsqueeze(-1)    # (B,C,1,1)
        return x * y


class ResFusionBlock(nn.Module):
    """
    Residual wrapper around two Conv3XC layers.
    - Optional stride on the first Conv3XC for learned downsampling.
    - ECA attention after the second Conv3XC.
    """
    def __init__(self, c_in: int, c_out: int, stride: int = 1, act=nn.SiLU(inplace=True), eca_k: int = 3):
        super().__init__()
        self.conv1 = Conv3XC(c_in, c_out, gain=2, s=stride, bias=True)
        self.act1 = act
        self.conv2 = Conv3XC(c_out, c_out, gain=2, s=1, bias=True)
        self.act2 = act
        self.attn = ECA(c_out, k=eca_k)

        self.proj = nn.Identity()
        if stride != 1 or c_in != c_out:
            # 1x1 projection for residual path when shape changes
            self.proj = nn.Conv2d(c_in, c_out, kernel_size=1, stride=stride, padding=0, bias=True)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        identity = self.proj(x)
        x = self.act1(self.conv1(x))
        x = self.act2(self.conv2(x))
        x = self.attn(x)
        return x + identity


class FinalModelFusionPlus(nn.Module):
    """
    Accuracy-focused variant:
      - Residual connections per stage (improves optimization/generalization).
      - ECA channel attention (cheap, robust) after each block.
      - Learned downsampling via stride-2 in block-1 of each stage (no MaxPool).
    Keeps the same (B,3,96,96)->(B,2) interface and deploy path.
    """
    def __init__(self, channels=(32, 64, 96, 128), eca_k=3):
        super().__init__()
        c1, c2, c3, c4 = channels
        # 96->48->24->12->6 using stride-2 in the first conv of each stage
        self.stem   = ResFusionBlock(3,  c1, stride=2, eca_k=eca_k)   # 96->48
        self.stage2 = ResFusionBlock(c1, c2, stride=2, eca_k=eca_k)   # 48->24
        self.stage3 = ResFusionBlock(c2, c3, stride=2, eca_k=eca_k)   # 24->12
        self.stage4 = ResFusionBlock(c3, c4, stride=2, eca_k=eca_k)   # 12->6

        self.head = nn.Sequential(
            nn.Flatten(),
            nn.Dropout(p=0.3),
            nn.Linear(c4 * 6 * 6, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(p=0.3),
            nn.Linear(256, 2),
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.stem(x)
        x = self.stage2(x)
        x = self.stage3(x)
        x = self.stage4(x)
        return self.head(x)

    @torch.no_grad()
    def active_inference(self):
        self.eval()
        for m in self.modules():
            if isinstance(m, Conv3XC):
                m.update_params(remove_unused=True)
                m.inference_mode = True
        return self

    @staticmethod
    @torch.no_grad()
    def from_fusable(src: "FinalModelFusionPlus", dst: "FinalModelDeploy") -> "FinalModelDeploy":
        """Fuse Conv3XC inside ResFusionBlocks and copy to deploy model."""
        src.eval(); dst.eval()
        # Ensure fusion ready
        for m in src.modules():
            if isinstance(m, Conv3XC) and not m.inference_mode:
                src.active_inference(); break

        def copy_block(dst_blk: DeployBlock, src_blk: ResFusionBlock):
            # copy conv1
            dst_blk.conv1.weight.copy_(src_blk.conv1.eval_conv.weight)
            dst_blk.conv1.bias.copy_(src_blk.conv1.eval_conv.bias)
            # copy conv2
            dst_blk.conv2.weight.copy_(src_blk.conv2.eval_conv.weight)
            dst_blk.conv2.bias.copy_(src_blk.conv2.eval_conv.bias)

        copy_block(dst.stem,   src.stem)
        copy_block(dst.stage2, src.stage2)
        copy_block(dst.stage3, src.stage3)
        copy_block(dst.stage4, src.stage4)

        # Copy head (no BN)
        dst.head[1].weight.copy_(src.head[2].weight)
        dst.head[1].bias.copy_(src.head[2].bias)
        dst.head[3].weight.copy_(src.head[4].weight)
        dst.head[3].bias.copy_(src.head[4].bias)
        return dst
