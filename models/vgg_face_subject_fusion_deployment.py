from time import perf_counter

import torch
from torch import nn

from torchinfo import summary
from fvcore.nn import FlopCountAnalysis, flop_count_table


class DeployBlock(nn.Module):
    """Inference-only block: two plain 3x3 convs (fused result of Conv3XC x2)."""
    def __init__(self, c_in, c_out, act=nn.SiLU(inplace=True)):
        super().__init__()
        self.conv1 = nn.Conv2d(c_in, c_out, kernel_size=3, stride=1, padding=1, bias=True)
        self.act1 = act
        self.conv2 = nn.Conv2d(c_out, c_out, kernel_size=3, stride=1, padding=1, bias=True)
        self.act2 = act

    def forward(self, x):
        x = self.act1(self.conv1(x))
        x = self.act2(self.conv2(x))
        return x


class FinalModelDeploy(nn.Module):
    """
    Inference-only version of FinalModel.
    - No training-time branches, only fused 3x3 convs.
    - Same I/O contract: (B, 3, 96, 96) -> (B, 2)
    - Parameters are set to requires_grad=False by default.
    """

    def __init__(self):
        super().__init__()
        # Stages identical to fused topology: 96->48->24->12->6
        self.stem = DeployBlock(3, 32)
        self.pool1 = nn.MaxPool2d(2)        # 96 -> 48
        self.stage2 = DeployBlock(32, 64)
        self.pool2 = nn.MaxPool2d(2)        # 48 -> 24
        self.stage3 = DeployBlock(64, 96)
        self.pool3 = nn.MaxPool2d(2)        # 24 -> 12
        self.stage4 = DeployBlock(96, 128)
        self.pool4 = nn.MaxPool2d(2)        # 12 -> 6
        
        self.head = nn.Sequential(
            nn.Flatten(),                   # (B, 128*6*6)
            nn.Linear(128 * 6 * 6, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, 2),              # pitch, yaw
        )

        # Default to inference-only: freeze grads
        for p in self.parameters():
            p.requires_grad = False

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.stem(x)
        x = self.pool1(x)
        
        x = self.stage2(x)
        x = self.pool2(x)
        
        x = self.stage3(x)
        x = self.pool3(x)
        
        x = self.stage4(x)
        x = self.pool4(x)
        
        return self.head(x)


if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    model = FinalModelDeploy()
    # weights_path = "runs/exp_?/best_model_fused.pt"
    weights_path = "best_model_fused.pt"
    model.load_state_dict(torch.load(weights_path, map_location=device, weights_only=True))
    model = model.to(device)
    model.eval()

    # Dummy input
    size = 96
    inputs = torch.randn(1, 3, size, size, device=device)

    # # FLOPs analysis
    flops = FlopCountAnalysis(model, inputs)
    print("==== FLOP Count Table ====")
    print(flop_count_table(flops))

    # Optional: more detailed summary
    print("\n==== torchinfo Summary ====")
    summary(model, input_size=(1, 3, size, size))
    
    print(f"FLOPs: {flops.total():,}\n")
    
    print(f"MFLOPs: {flops.total() / 1e6:.3f}")
    print(f"GFLOPs: {flops.total() / 1e9:.3f}")
    print(f"TFLOPs: {flops.total() / 1e12:.6f}")
    
    TFLOPS_MAX = 4
    max_inferences_per_second = TFLOPS_MAX / (flops.total() / 1e12)
    print(f"≈ {max_inferences_per_second:.0f} inferences per second on TI board")

    # Estimate inference time
    x = torch.randn(1, 3, 96, 96).to(device)
    start = perf_counter()
    for i in range(1_000):
        with torch.no_grad():
            model(x)
    end = perf_counter()
    inference_time = round(1 / ((end - start) / 1_000))
    print(f"Inference time: {inference_time:,} FPS")
"""

"""
