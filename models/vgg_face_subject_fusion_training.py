from time import perf_counter

import torch
from torch import nn
from torch.nn import functional as F
from torchinfo import summary
from fvcore.nn import FlopCountAnalysis, flop_count_table

from models.vgg_face_subject_fusion_deployment import FinalModelDeploy, DeployBlock


class Conv3XC(nn.Module):
    """
    Fusable 3x3 Conv built from (1x1 -> 3x3 -> 1x1) with a parallel 1x1 skip.
    During training it runs the explicit multi-conv path. For inference, the
    three layers + skip can be re-parameterized (fused) into a single 3x3 conv
    with identical outputs, reducing FLOPs/params at deploy time.

    Call `update_params(remove_unused=True)` once (or `model.active_inference()`)
    before export/deploy to materialize the fused 3x3 weights/bias.
    """

    def __init__(self, c_in, c_out, gain=2, s=1, bias=True, relu=False):
        super().__init__()
        self.stride = s
        self.has_relu = relu

        # Parallel 1x1 skip path (re-parameterizable into 3x3 via zero-padding)
        self.sk = nn.Conv2d(c_in, c_out, kernel_size=1, padding=0, stride=s, bias=bias)

        # 1x1 -> 3x3 (no padding, we pad input explicitly) -> 1x1
        self.conv = nn.Sequential(
            nn.Conv2d(c_in, c_in * gain, kernel_size=1, padding=0, bias=bias),
            nn.Conv2d(c_in * gain, c_out * gain, kernel_size=3, stride=s, padding=0, bias=bias),
            nn.Conv2d(c_out * gain, c_out, kernel_size=1, padding=0, bias=bias),
        )

        # Single 3x3 used for evaluation/inference after fusion
        self.eval_conv = nn.Conv2d(c_in, c_out, kernel_size=3, padding=1, stride=s, bias=bias)
        self.eval_conv.weight.requires_grad = False
        self.eval_conv.bias.requires_grad = False
        self.inference_mode = False

    @torch.no_grad()
    def update_params(self, remove_unused: bool = False):
        """Fuse (1x1 -> 3x3 -> 1x1) + skip(1x1) into a single 3x3.
        Optionally strip the original layers to save memory.
        """
        # Extract weights/biases
        w1, b1 = self.conv[0].weight.data.clone(), self.conv[0].bias.data.clone()
        w2, b2 = self.conv[1].weight.data.clone(), self.conv[1].bias.data.clone()
        w3, b3 = self.conv[2].weight.data.clone(), self.conv[2].bias.data.clone()

        # Fuse first two convs: effective 3x3 in the middle space
        w12 = F.conv2d(w1.flip(2, 3).permute(1, 0, 2, 3), w2, padding=2, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        b12 = (w2 * b1.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b2

        # Fuse with final 1x1
        w123 = F.conv2d(w12.flip(2, 3).permute(1, 0, 2, 3), w3, padding=0, stride=1).flip(2, 3).permute(1, 0, 2, 3)
        b123 = (w3 * b12.reshape(1, -1, 1, 1)).sum((1, 2, 3)) + b3

        # Bring in the skip 1x1 as a zero-padded 3x3 and add
        sk_w = self.sk.weight.data.clone()
        sk_b = self.sk.bias.data.clone()
        sk_w = F.pad(sk_w, [1, 1, 1, 1])  # pad to 3x3

        fused_w = w123 + sk_w
        fused_b = b123 + sk_b

        # Materialize
        self.eval_conv.weight.data.copy_(fused_w)
        self.eval_conv.bias.data.copy_(fused_b)

        if remove_unused:
            self.conv = nn.Identity()
            self.sk = nn.Identity()
            self.inference_mode = True

    def forward(self, x):
        if self.training:
            # explicit padding for the middle 3x3
            x_pad = F.pad(x, (1, 1, 1, 1), value=0.0)
            out = self.conv(x_pad) + self.sk(x)
        else:
            if not self.inference_mode:
                # lazily fuse on first eval forward for identical outputs
                self.update_params(remove_unused=False)
            out = self.eval_conv(x)
        
        if self.has_relu:
            out = F.leaky_relu(out, negative_slope=0.05)
        return out


class FusionBlock(nn.Module):
    """Two Conv3XC layers with activation, used as a lightweight stage unit."""
    def __init__(self, c_in, c_out, act=nn.SiLU(inplace=True)):
        super().__init__()
        self.conv1 = Conv3XC(c_in, c_out, gain=2, s=1, bias=True)
        self.act1 = act
        self.conv2 = Conv3XC(c_out, c_out, gain=2, s=1, bias=True)
        self.act2 = act

    def forward(self, x):
        x = self.act1(self.conv1(x))
        x = self.act2(self.conv2(x))
        return x


class FinalModelFusion(nn.Module):
    """
    Gaze model with Conv-Fusion backbone (fusable Conv3XC blocks) for low-FLOPs deploy.

    Input:  (B, 3, 96, 96)
    Output: (B, 2)  -> (pitch, yaw)

    The spatial pipeline downsamples to a 6x6 feature map with 128 channels,
    matching the original FC head interface to minimize training code changes.

    Call `active_inference()` before ONNX/TorchScript export to fuse blocks into
    single 3x3 convs for inference on resource-constrained devices (e.g., TI board).
    Usage:
        # 1) Convert from a trained FinalModel
        src = FinalModelFusion()
        dst = FinalModelDeploy()
        model_deploy = model.from_fusable(src, dst)

        # 2) Save deployment weights only
        torch.save(model_deploy.state_dict(), "best_model_fused.pth")

        # 3) Load for inference elsewhere
        model_deploy = FinalModelDeploy()
        model_deploy.load_state_dict(torch.load("best_model_fused.pth", map_location=device))
        model_deploy.eval().to(device)
    """

    def __init__(self):
        super().__init__()

        # Stages: 96->48->24->12->6
        self.stem = FusionBlock(3, 32)
        self.pool1 = nn.MaxPool2d(2)  # 96 -> 48

        self.stage2 = FusionBlock(32, 64)
        self.pool2 = nn.MaxPool2d(2)  # 48 -> 24

        self.stage3 = FusionBlock(64, 96)
        self.pool3 = nn.MaxPool2d(2)  # 24 -> 12

        self.stage4 = FusionBlock(96, 128)
        self.pool4 = nn.MaxPool2d(2)  # 12 -> 6

        # FC head kept compatible with previous implementation
        self.head = nn.Sequential(
            nn.Flatten(),                 # (B, 128*6*6)
            nn.Dropout(p=0.5),
            nn.Linear(128 * 6 * 6, 256),
            nn.ReLU(inplace=True),
            nn.BatchNorm1d(256),
            nn.Dropout(p=0.5),
            nn.Linear(256, 2),           # pitch, yaw
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.stem(x)
        x = self.pool1(x)

        x = self.stage2(x)
        x = self.pool2(x)

        x = self.stage3(x)
        x = self.pool3(x)

        x = self.stage4(x)
        x = self.pool4(x)  # -> (B, 128, 6, 6)

        out = self.head(x)
        return out

    @torch.no_grad()
    def active_inference(self):
        """Fuse all Conv3XC blocks so evaluation uses single 3x3 ops."""
        self.eval()
        for m in self.modules():
            if isinstance(m, Conv3XC):
                m.update_params(remove_unused=True)
                m.inference_mode = True
        return self

    @staticmethod
    @torch.no_grad()
    def from_fusable(src: "FinalModelFusion", dst: "FinalModelDeploy") -> "FinalModelDeploy":
        """Build a deploy model and copy fused weights from a trained FinalModel.
        Fuses Conv3XC blocks and folds BatchNorm in the head into the following Linear.
        """
        src.eval()
        dst.eval()

        # Ensure fusion has materialized the 3x3 weights
        if any(isinstance(m, Conv3XC) and (not m.inference_mode) for m in src.modules()):
            src.active_inference()

        def copy_block(dst_block: DeployBlock, src_block: FusionBlock):
            # copy conv1
            dst_block.conv1.weight.copy_(src_block.conv1.eval_conv.weight)
            dst_block.conv1.bias.copy_(src_block.conv1.eval_conv.bias)
            # copy conv2
            dst_block.conv2.weight.copy_(src_block.conv2.eval_conv.weight)
            dst_block.conv2.bias.copy_(src_block.conv2.eval_conv.bias)

        copy_block(dst.stem,   src.stem)
        copy_block(dst.stage2, src.stage2)
        copy_block(dst.stage3, src.stage3)
        copy_block(dst.stage4, src.stage4)

        # ---- Fold BN into the following Linear in the head ----
        # src head layout: [0]=Flatten, [1]=Dropout, [2]=Linear(4608->256), [3]=ReLU,
        #                  [4]=BatchNorm1d(256), [5]=Dropout, [6]=Linear(256->2)
        # dst head layout: [0]=Flatten, [1]=Linear(4608->256), [2]=ReLU, [3]=Linear(256->2)

        # 1) Copy first Linear unchanged
        dst.head[1].weight.copy_(src.head[2].weight)
        dst.head[1].bias.copy_(src.head[2].bias)

        # 2) Fold BN (after ReLU in src) into the *next* Linear (256->2)
        bn = src.head[4]
        W2 = src.head[6].weight.clone()  # (2,256)
        b2 = src.head[6].bias.clone()    # (2,)

        gamma = bn.weight.clone()        # (256,)
        beta  = bn.bias.clone()          # (256,)
        mean  = bn.running_mean.clone()  # (256,)
        var   = bn.running_var.clone()   # (256,)
        eps   = bn.eps

        sigma = (var + eps).sqrt()       # (256,)
        a = gamma / sigma                # (256,)
        c = beta - gamma * mean / sigma  # (256,)

        # W2' = W2 * diag(a);  b2' = b2 + W2 @ c
        W2p = W2 * a.unsqueeze(0)        # broadcast over columns
        b2p = b2 + torch.matmul(W2, c)

        dst.head[3].weight.copy_(W2p)
        dst.head[3].bias.copy_(b2p)

        return dst


if __name__ == "__main__":
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 1) Fuse the model for deployment
    src = FinalModelFusion().eval().to(device)
    dst = FinalModelDeploy().eval().to(device)
    fused_model = src.from_fusable(src=src, dst=dst)
    
    # 2) Save deployment weights only
    weights_path = "best_model_fused.pt"
    torch.save(fused_model.state_dict(), weights_path)
    
    # 3) Load for inference elsewhere
    model = FinalModelDeploy()
    model.load_state_dict(torch.load(weights_path, map_location=device, weights_only=True))
    model.eval().to(device)
    
    # Dummy input
    size = 96
    inputs = torch.randn(1, 3, size, size, device=device)

    # FLOPs analysis
    flops = FlopCountAnalysis(model, inputs)
    print("==== FLOP Count Table ====")
    # print(flop_count_table(flops))

    # Optional: more detailed summary
    print("\n==== torchinfo Summary ====")
    summary(model, input_size=(1, 3, size, size))
    
    print(f"FLOPs: {flops.total():,}\n")
    
    print(f"MFLOPs: {flops.total() / 1e6:.3f}")
    print(f"GFLOPs: {flops.total() / 1e9:.3f}")
    print(f"TFLOPs: {flops.total() / 1e12:.6f}")
    
    TFLOPS_MAX = 4
    max_inferences_per_second = TFLOPS_MAX / (flops.total() / 1e12)
    print(f"≈ {max_inferences_per_second:.0f} inferences per second on TI board")

    # Estimate inference time
    x = torch.randn(1, 3, 96, 96).to(device)
    
    # For original model
    start = perf_counter()
    for i in range(1_000):
        with torch.no_grad():
            src(x)
    end = perf_counter()
    inference_time = round(1 / ((end - start) / 1_000))
    print(f"Time: {inference_time:,} FPS")
    
    # For fused model
    start = perf_counter()
    for i in range(1_000):
        with torch.no_grad():
            model(x)
    end = perf_counter()
    inference_time = round(1 / ((end - start) / 1_000))
    print(f"Time: {inference_time:,} FPS")
    # ==============================================================================
    # Optional quick equivalence check (numerical parity)
    torch.manual_seed(0)
    
    x = torch.randn(32, 3, 96, 96).to(device)
    with torch.no_grad():
        y1 = src(x)
        y2 = model(x)
        
    max_diff = (y1 - y2).abs().max().item()

    # Alert if exceeds threshold
    threshold = 1e-6
    if max_diff > threshold:
        raise ValueError(f"Model fusion error too high! Max diff = {max_diff:.9f}")
    else:
        print(f"Fusion OK ✅ | Max diff = {max_diff:.9f}")

"""
Before fusion: (aplied only for training mode)
==========================================================================================
Total params: 4,475,930
Trainable params: 3,884,410 (Actual "Total params" - since we could don't use conv_eval for training)
Non-trainable params: 591,520
Total mult-adds (Units.GIGABYTES): 1.57
==========================================================================================
Input size (MB): 0.11
Forward/backward pass size (MB): 44.69
Params size (MB): 13.18
Estimated Total Size (MB): 57.98
==========================================================================================
FLOPs: 1,566,615,368

MFLOPs: 1566.615
GFLOPs: 1.567
TFLOPs: 0.001567
≈ 2553 inferences per second on TI board

------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------

After fusion:
==========================================================================================
Total params: 1,771,938
Trainable params: 0
Non-trainable params: 1,771,938
Total mult-adds (Units.MEGABYTES): 339.30
==========================================================================================
Input size (MB): 0.11
Forward/backward pass size (MB): 8.26
Params size (MB): 6.57
Estimated Total Size (MB): 14.94
==========================================================================================
FLOPs: 338,264,576

MFLOPs: 338.265
GFLOPs: 0.338
TFLOPs: 0.000338
≈ 11825 inferences per second on TI board
Fusion OK ✅ | Max diff = 0.000000004
"""
