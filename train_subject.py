# === Standard Libraries ===
import os
import yaml
import copy
import logging
import argparse
from tqdm import tqdm

# === Third-Party Libraries ===
import torch
import torch.nn as nn
import torchvision.models as models

# === Local Modules ===
from utils import util
from utils.datasets_subject import get_dataloaders
from utils.helpers import calculate_gaze_angle_error
from models.vgg_face_subject import FinalModel


"""
Default

MPII
    -- same as default
    - Select model
    - get_dataloaders for MPII
    - data path: data/MPIIFaceGaze_96_scaleTrue_FDN500-600
    - select training parametes
        - batch size: 256
        - num epochs: 100
        - lr: 0.001
        - note: MPIIFaceGaze_96_scaleTrue_FDN500-600
Fuse
    - same as default
    + choosed modifed model
    + use fusion in the end
"""


util.setup_multi_processes()
util.init_deterministic_seed()


def parse_args():
    """Parse input arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--data",           type=str,   default="data/dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5")
    parser.add_argument("--save-dir",       type=str,   default="runs")
    parser.add_argument("--weights",        type=str,   default="runs/exp_189/best_model.pt")
    parser.add_argument("--num-epochs",     type=int,   default=100)
    parser.add_argument("--batch-size",     type=int,   default=32)
    parser.add_argument("--lr",             type=float, default=0.001)
    parser.add_argument("--alpha",          type=float, default=0.0)
    parser.add_argument("--beta",           type=float, default=1.0)
    parser.add_argument("--gamma",          type=float, default=0.0)
    parser.add_argument("--num-workers",    type=int,   default=8)
    parser.add_argument('--opset-version',  type=int,   default=11)
    parser.add_argument("--note",           type=str,   default="?")
    parser.add_argument("--use_torch_model",  action="store_true")
    parser.add_argument("--train",  action="store_true")
    parser.add_argument("--test",   action="store_true")
    args = parser.parse_args()
    return args


def make_first_conv_1ch_regnet(m: nn.Module) -> nn.Module:
    """
    Replace RegNet's stem conv (3->C) with a 1->C conv
    and initialize weights by averaging across RGB.
    """
    # RegNet in torchvision uses a SimpleStemIN with .conv/.bn/.relu
    stem = m.stem  # SimpleStemIN: (0)=Conv2d, (1)=BN, (2)=ReLU
    old_conv: nn.Conv2d = stem[0]          # in_channels = 3
    w = old_conv.weight.data               # [32, 3, k, k]

    # New conv: same hyperparams, but in_channels=1
    new_conv = nn.Conv2d(
        in_channels=1,
        out_channels=old_conv.out_channels,
        kernel_size=old_conv.kernel_size,
        stride=old_conv.stride,
        padding=old_conv.padding,
        dilation=old_conv.dilation,
        groups=old_conv.groups,
        bias=False,
        padding_mode=old_conv.padding_mode
    )

    with torch.no_grad():
        # Average RGB -> single channel (keeps luminance-ish content)
        new_conv.weight.copy_(w.mean(dim=1, keepdim=True))

    # Swap into the stem
    stem[0] = new_conv
    
    return m


def initialize_model(args, device):
    """
    Initialize the gaze estimation model, optimizer, and optionally load a checkpoint.

    Args:
        args (argparse.Namespace): Parsed command-line arguments.
        device (torch.device): Device to load the model and optimizer onto.

    Returns:
        Tuple[nn.Module, torch.optim.Optimizer, int]: Initialized model, optimizer, and the starting epoch.
    """
    if args.use_torch_model == True:
        # for i, model_name in enumerate(models.list_models()):
        #     print(f"{i}: {model_name}")

        # Initialize model
        model = models.get_model(name="regnet_x_800mf", weights=models.RegNet_X_800MF_Weights.DEFAULT)
        model = make_first_conv_1ch_regnet(model)
        model.fc = torch.nn.Linear(model.fc.in_features, 2)
  
        # Initialize the regression layer
        torch.nn.init.normal_(model.fc.weight, std=0.001)
        if model.fc.bias is not None:
            torch.nn.init.constant_(model.fc.bias, 0)
        
    else:
        model = FinalModel()
        model_dict = model.state_dict()

        # author_subject_weights = "weights/MPIIFaceGaze_96_scaleTrue_FDN500-600.pt"
        author_subject_weights = "weights/author_subject-independent.ckpt"
        snapshot = torch.load(author_subject_weights, map_location=device, weights_only=True)

        # snapshot = {k: v for k, v in snapshot.items() if k in model_dict and v.shape == model_dict[k].shape}
        snapshot = {k: v for k, v in snapshot["state_dict"].items() if k in model_dict and v.shape == model_dict[k].shape}
        model_dict.update(snapshot)
        model.load_state_dict(model_dict)
    
    # Optimizer - version 1
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=0.0,
        eps=1e-8,           # Prevent division by zero
        betas=(0.9, 0.999)  # Standard Adam parameters
    )
    
    # Freeze stem part via param.requires_grad
    # for param in util.get_params(model, part="cnn_face", require_grad_only=False):
    #     param.requires_grad = False

    # Optimizer - version 2
    # optimizer = torch.optim.Adam([
    #     {'params': util.get_params(model, part="cnn_face", require_grad_only=False), 'lr': args.lr},
    #     {'params': util.get_params(model, part="fc_face", require_grad_only=True), 'lr': args.lr},
    # ], lr=args.lr, weight_decay=5e-4)
    
    # Scheduler - version 1
    # scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.num_epochs)
    
    # Scheduler - version 2
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=10,
        min_lr=1e-7
    )
    
    return model.to(device), optimizer, scheduler


def train_one_epoch(args, model, reg_criterion, optimizer, data_loader, device):
    """
    Train the model for one epoch.

    Args:
        model (nn.Module): The gaze estimation model.
        reg_criterion (nn.Module): Loss function for regression.
        optimizer (torch.optim.Optimizer): Optimizer for the model.
        data_loader (DataLoader): DataLoader for the training dataset.
        device (torch.device): Device to perform training on.

    Returns:
        Tuple[float, float]: Average losses for pitch and yaw.
    """
    model.train()
    sum_loss = 0
    sum_angular_error = 0
    total_num_samples = 0
    num_batches = len(data_loader)
    
    for images, labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        labels = labels.to(device)

        # Forward pass
        outputs = model(images)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(labels, outputs)

        # Use L1 loss instead of MSE for better stability
        mse_loss = reg_criterion(labels, outputs)

        # Combine losses with safety check
        loss = (args.alpha * loss_ang) + (args.beta * mse_loss)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        # ==================================================================
        
        sum_loss += loss.item() * labels.size(0)
        sum_angular_error += angular_error.item() * labels.size(0)
        total_num_samples += labels.size(0)
    
    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    return avg_loss, avg_angular_error


@torch.no_grad()
def evaluate(args, model, reg_criterion, data_loader, device, mode="val"):
    """
    Evaluate the model on the test dataset.

    Args:
        model (nn.Module): The gaze estimation model.
        reg_criterion (nn.Module): Loss function for regression.
        data_loader (torch.utils.data.DataLoader): DataLoader for the test dataset.
        device (torch.device): Device to perform evaluation on.
        mode (str): Evaluation mode ("val" or "test").
    """
    model.eval()
    sum_loss = 0
    sum_angular_error = 0
    total_num_samples = 0
    num_batches = len(data_loader)

    for images, labels in tqdm(data_loader, total=num_batches):
        images = images.to(device)
        labels = labels.to(device)

        # Forward pass
        outputs = model(images)
        
        # Calculate losses with configurable weights and safety checks
        loss_ang, angular_error = calculate_gaze_angle_error(labels, outputs)

        # Use L1 loss instead of MSE for better stability
        mse_loss = reg_criterion(labels, outputs)

        # Combine losses with safety check
        loss = (args.alpha * loss_ang) + (args.beta * mse_loss)
        
        sum_loss += loss.item() * labels.size(0)
        sum_angular_error += angular_error.item() * labels.size(0)
        total_num_samples += labels.size(0)

    # Average loss and angular error regarding batch size
    avg_loss = sum_loss / total_num_samples
    avg_angular_error = sum_angular_error / total_num_samples
    
    if mode == "test":
        logging.info(
            f"Total Number of Samples: {total_num_samples} | "
            f"Loss: {avg_loss:.7f}°  |  "
            f"Angular Vector Error: {avg_angular_error:.7f}°"
        )
    
    return avg_loss, avg_angular_error


def main():
    args = parse_args()

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    args.save_dir = util.set_experiment_results_output(args, root="runs")
    util.safe_yaml_config_file(args)

    # Initialize model, optimizer, and optionally load a checkpoint
    model, optimizer, scheduler = initialize_model(args, device)

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)

    # Loss functions
    reg_criterion = nn.MSELoss()

    best_model_path = None
    best_error = float('inf')
    performance_results = []

    if args.train:
        # Training loop
        for epoch in range(args.num_epochs):
            # Unfreeze fc_face
            # if epoch == 3:
            #     for param in util.get_params(model, part="fc_face", require_grad_only=False):
            #         param.requires_grad = True
            
            # Train the model for the current epoch
            train_loss, train_angular_error = train_one_epoch(
                args,
                model,
                reg_criterion,
                optimizer,
                train_loader,
                device,
            )
            
            # Evaluate on validation set for the current fold
            val_loss, val_angular_error = evaluate(args, model, reg_criterion, val_loader, device, mode="val")
            
            # Step the scheduler after each epoch
            current_lrs = " | ".join([f"{group['lr']:.0e}" for group in optimizer.param_groups])
            scheduler.step(val_angular_error)
        
            # Save the best model
            if val_angular_error < best_error:
                best_error = val_angular_error
                best_model_path = os.path.join(args.save_dir, 'best_model.pt')
                torch.save(model.state_dict(), best_model_path)
                
                # Save best onnx model
                model_onnx = copy.deepcopy(model).float().eval()
                if hasattr(model_onnx, 'fuse'): model_onnx = model_onnx.fuse()
                example_input = torch.randn(1, 1, 96, 96).to(args.device)
                onnx_output_path = os.path.join(args.save_dir, 'best_model.onnx')
                util.export_onnx_model(args, model_onnx, example_input, onnx_output_path)
    
                status = "*"
            else:
                status = ""

            # Log the results
            logging.info(
                f'Epoch [{epoch + 1}/{args.num_epochs}] '
                f'Losses: {train_loss:.4f} / {val_loss:.4f}  |  '
                f'Angular Vector Error: {train_angular_error:.1f}° / {val_angular_error:.1f}°  |  '
                f'LR: {current_lrs}  |  {status}'
            )
        
            # Save the results for plotting
            result = (epoch, float(train_loss), float(val_loss), float(train_angular_error), float(val_angular_error))
            performance_results.append(result)
        
            # Plot the results
            util.plot_performance_results_splited(args, performance_results)

    if args.test:
        # Test the best model
        if best_model_path is None:
            best_model_path = args.weights
        
        if os.path.exists(best_model_path):
            logging.info(f'Best model saved at {best_model_path}')
            model.load_state_dict(torch.load(best_model_path, map_location=device, weights_only=True))
        else:
            raise ValueError(f"Model weight not found at {best_model_path}")

        logging.info("Start Testing")
        val_loss, val_angular_error = evaluate(args, model, reg_criterion,test_loader, device, mode="test")
        
        if args.train:
            result = (epoch+1, float(train_loss), float(val_loss), float(train_angular_error), float(val_angular_error))
            performance_results.append(result)
            util.plot_performance_results_splited(args, performance_results)


if __name__ == '__main__':
    main()
