import cv2
import numpy as np
import albumentations as A


def equalize_hist_rgb(rgb_img: np.ndarray) -> np.ndarray:
    ycrcb_img = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2YCrCb)
    ycrcb_img[:, :, 0] = cv2.equalizeHist(ycrcb_img[:, :, 0])
    equalized_img = cv2.cvtColor(ycrcb_img, cv2.COLOR_YCrCb2RGB)
    return equalized_img


def lab_equalize_hist(rgb_img: np.ndarray) -> np.ndarray:
    lab = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2Lab)
    lab[:, :, 0] = cv2.equalizeHist(lab[:, :, 0])
    result = cv2.cvtColor(lab, cv2.COLOR_Lab2RGB)
    return result


def clahe(rgb_img: np.ndarray):
    lab = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2Lab)

    # Split LAB channels
    L, A, B = cv2.split(lab)

    # Create CLAHE object
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(2, 2))

    # Apply CLAHE to L channel
    L_clahe = clahe.apply(L)

    # Merge and convert back to BGR
    lab_clahe = cv2.merge((L_clahe, A, B))
    result = cv2.cvtColor(lab_clahe, cv2.COLOR_Lab2RGB)
    return result


class SafeCoarseDropout(A.ImageOnlyTransform):
    def __init__(
        self,
        holes_range=(1, 5),
        height_ratio=(0.1, 0.3),
        width_ratio=(0.1, 0.3),
        fill_value=0,               # [0, 255, or 'random']
        avoid_radius_ratio=0.1,
        p=0.5,
    ):
        super().__init__()
        self.holes_range = holes_range
        self.height_ratio = height_ratio
        self.width_ratio = width_ratio
        self.fill_value = fill_value
        self.avoid_radius_ratio = avoid_radius_ratio
        self.p = p

    def apply(self, image, eye_coords=None, **params):
        if np.random.random() > self.p:
            return image
        
        if eye_coords is None:
            return image  # skip if no eye coordinates

        h, w = image.shape[:2]

        eye_px = [(int(x * w), int(y * h)) for x, y in eye_coords]
        avoid_radius = int(min(w, h) * self.avoid_radius_ratio)

        hole_ratio_h = np.random.uniform(self.height_ratio[0], self.height_ratio[1])
        hole_ratio_w = np.random.uniform(self.width_ratio[0], self.width_ratio[1])

        max_hole_h = int(h * hole_ratio_h)
        max_hole_w = int(w * hole_ratio_w)

        output = image.copy()
        num_holes = np.random.randint(self.holes_range[0], self.holes_range[1])

        for _ in range(num_holes):
            tries = 0
            while tries < 20:
                x = np.random.randint(0, w - max_hole_w)
                y = np.random.randint(0, h - max_hole_h)

                overlaps = False
                for ex, ey in eye_px:
                    if (
                        x - avoid_radius < ex < x + max_hole_w + avoid_radius and
                        y - avoid_radius < ey < y + max_hole_h + avoid_radius
                    ):
                        overlaps = True
                        break

                if not overlaps:
                    if self.fill_value == 'random':
                        color = np.random.randint(0, 256, size=(max_hole_h, max_hole_w, 3), dtype=np.uint8)
                    else:
                        color = np.full((max_hole_h, max_hole_w, 3), self.fill_value, dtype=np.uint8)
                    output[y:y + max_hole_h, x:x + max_hole_w] = color
                    break
                tries += 1

        return output


class SafeFlip(A.ImageOnlyTransform):
    def __init__(self, p=0.5):
        super().__init__()
        self.p = p

    def pitchyaw_to_gaze_vector(self, gaze: tuple) -> np.ndarray:
        """
        Convert pitch and yaw angles to a 3D gaze direction vector.

        Args:
            pitch: Pitch angle in radians
            yaw: Yaw angle in radians

        Returns:
            3D gaze direction vector as numpy array [x, y, z]
        """
        pitch, yaw = gaze
        
        x = -np.cos(pitch) * np.sin(yaw)
        y = -np.sin(pitch)
        z = -np.cos(pitch) * np.cos(yaw)
        return np.array([x, y, z])

    def gaze_vector_to_pitchyaw(self, gaze_vector: np.ndarray) -> tuple:
        x, y, z = gaze_vector
        pitch = np.arcsin(-y)

        # Yaw: angle between projection of gaze vector on XZ plane and Z axis
        yaw = np.arctan2(-x, -z)
        return (pitch, yaw)

    def apply(self, image, gaze=None, **params):
        if np.random.random() > self.p:
            return image, gaze
        
        if gaze is None:
            return image, gaze  # skip if no eye coordinates

        # Flip image
        image = cv2.flip(image, flipCode=1)
        
        # Flip gaze vector
        gaze_vector = self.pitchyaw_to_gaze_vector(gaze)
        gaze_vector[0] = -gaze_vector[0]  # Flip X component
        gaze = self.gaze_vector_to_pitchyaw(gaze_vector)

        return image, gaze


class GazeSafeAugmentation:
    """Wrapper class to handle eye coordinates with Albumentations transforms."""

    def __init__(self, image_size=96):
        self.safe_dropout = SafeCoarseDropout(
            holes_range=(1, 10),
            height_ratio=(0.10, 0.30),
            width_ratio=(0.10, 0.30),
            fill_value='random',
            avoid_radius_ratio=0.10,
            p=0.3,
        )
        self.safe_flip = SafeFlip(p=0.0)
        
        self.other_transforms = A.Compose([
            A.Resize(height=image_size, width=image_size), # for MPII
            
            # ----------------------------------------
            # 📦 Color & Lighting Augmentations
            A.OneOf([
                A.RandomBrightnessContrast(brightness_limit=(-0.2, 0.2), contrast_limit=(-0.2, 0.2), p=0.7),
                A.CLAHE(clip_limit=1.0, tile_grid_size=(4, 4), p=0.3),
            ], p=0.8),

            A.HueSaturationValue(hue_shift_limit=(-4, 4), sat_shift_limit=(-6, 6), val_shift_limit=(-6, 6), p=0.5),

            # # ----------------------------------------
            # # 🔉 Noise & Blur
            A.OneOf([
                A.GaussNoise(std_range=(0.05, 0.1), p=0.3),
                A.MultiplicativeNoise(multiplier=(0.98, 1.02), p=0.3),
                A.GaussianBlur(blur_limit=3, p=0.2),
                A.MotionBlur(blur_limit=3, p=0.2),
                A.MedianBlur(blur_limit=3, p=0.2),
            ], p=0.5),

            # ----------------------------------------
            # 🎛 Channel-Based Noise
            A.OneOf([
                A.RGBShift(r_shift_limit=5, g_shift_limit=5, b_shift_limit=5, p=0.5),
                A.ChannelShuffle(p=0.1),
            ], p=0.5),

            # ----------------------------------------
            # 🕶 Partial Occlusions (Avoid eyes!)
            # A.CoarseDropout(
            #     num_holes_range=(1, 5),
            #     hole_height_range=(10, 20),
            #     hole_width_range=(10, 20),
            #     fill=0,
            #     p=0.3,
            # ),
            
            # ----------------------------------------
            # 🧪 JPEG Compression Artifact Simulation
            A.ImageCompression(quality_range=(90, 100), p=0.3),
            
            # ----------------------------------------
            A.Normalize(),              # mean=[0.0, 0.0, 0.0], std=[1.0, 1.0, 1.0] by default
            A.pytorch.ToTensorV2(),     # Convert HWC to CHW and np.uint8 → torch.float32
        ], seed=42)

    def __call__(self, image, eye_coords=None, gaze=None, **kwargs):
        """Apply augmentations with eye coordinate awareness."""
        # First apply the safe dropout with eye coordinates
        if eye_coords is not None:
            image = self.safe_dropout.apply(image, eye_coords=eye_coords)
        if gaze is not None:
            image, gaze = self.safe_flip.apply(image, gaze=gaze)

        # Then apply other transforms
        image = self.other_transforms(image=image)["image"]
        return {"image": image, "gaze": gaze}
