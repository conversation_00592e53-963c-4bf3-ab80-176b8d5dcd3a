import sys
from pathlib import Path

root_dir = Path(__file__).resolve().parent.parent
sys.path.append(str(root_dir))
# === Standard Libraries ===
import os
import random
import argparse
    
# === Third-Party Libraries ===
import cv2
import torch
import skimage.io
import numpy as np
import pandas as pd
from torch.utils.data import Dataset

# === Local Modules ===
from utils.augmentation import GazeSafeAugmentation, clahe
from utils.datasets_binned import CustomGazeDataset as CustomGazeDatasetBinned
from utils.datasets_subject import CustomGazeDataset as CustomGazeDatasetSubject


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--data', type=str, default='../data/dataset_normalized_merged_v1-v2-v3_SCALE_FDN500-600')  # Update with actual path
    parser.add_argument('--batch_size',     type=int, default=1)
    parser.add_argument('--num_workers',    type=int, default=0)
    parser.add_argument('--angle',          type=int, default=30)
    parser.add_argument('--binwidth',       type=int, default=3)
    args = parser.parse_args()

    # Load dataset with augmentations
    dataset = CustomGazeDatasetSubject(
        data_path=os.path.join(args.data, 'train'),
        transform=GazeSafeAugmentation(),
    )

    # Create shuffled list of indices
    indices = list(range(len(dataset)))
    random.seed(42)
    random.shuffle(indices)

    output_dir = "../aug"
    os.makedirs(output_dir, exist_ok=True)
    
    k = 0
    for i in indices:  # Limit to first 10 shuffled samples
        image, binned_label, regression_label = dataset[i]
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        cv2.imwrite(f"{output_dir}/debug_output_{k:03d}.jpg", image)
        
        # Optional: Stop early
        k += 1
        if k == 10:
            print("Saved first 10 images. Exiting.")
            break
        
        # # Show image
        # cv2.imshow("Augmented Gaze Image", image)
        # key = cv2.waitKey(0)

        # if key == ord('q'):
        #     print("Exiting viewer.")
        #     break

    # cv2.destroyAllWindows()
