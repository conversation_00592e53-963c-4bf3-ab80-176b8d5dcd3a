# === Standard Libraries ===
import os

# === Third-Party Libraries ===
import torch
import skimage.io
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

# === Local Modules ===
from utils import util
from utils.augmentation import GazeSafeAugmentation, clahe


class CustomGazeDataset(Dataset):
    """
    PyTorch Dataset for normalized gaze tracking data.

    This dataset loads face images along with corresponding gaze labels
    from a structured directory format.

    Expected directory structure:
        <data_root>/
        ├── labels.csv
        └── pXX/
            └── <basename>-face.jpg

    CSV format:
        - face_file_name: Relative path to face image
        - pitch: Gaze pitch angle in radians
        - yaw: Gaze yaw angle in radians

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None, angle=42, binwidth=3):
        self.data_path = data_path
        self.transform = transform
        self.angle = angle
        self.binwidth = binwidth
        self.df = pd.read_csv(f"{data_path}/labels.csv")

        # Filter out samples with angles outside the desired range
        pitch_deg = np.degrees(self.df['pitch'])
        yaw_deg = np.degrees(self.df['yaw'])
        
        valid_indices = (abs(pitch_deg) <= angle) & (abs(yaw_deg) <= angle)
        orig_list_len = len(self.df)
        self.df = self.df[valid_indices].reset_index(drop=True)

        removed_items = orig_list_len - len(self.df)
        print(f"{removed_items} items removed from dataset that have an angle > {angle}")
        print(f"Pitch | min-avg-max: [{pitch_deg.min():.1f}, {pitch_deg.mean():.1f}, {pitch_deg.max():.1f}]")
        print(f"Yaw   | min-avg-max: [{yaw_deg.min():.1f}, {yaw_deg.mean():.1f}, {yaw_deg.max():.1f}]\n")
        
    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Load face image
        face = skimage.io.imread(f"{self.data_path}/{row.face_file_name}")
        eye_coords = [
            (row.normalized_left_eye_center_x, row.normalized_left_eye_center_y),
            (row.normalized_right_eye_center_x, row.normalized_right_eye_center_y)
        ]

        # Apply transforms
        face = clahe(face)
        image = self.transform(image=face, eye_coords=eye_coords)["image"]

        # Get labels (already in radians from CSV)
        pitch = row.pitch
        yaw = row.yaw

        # Convert to degrees for binning
        pitch_deg = np.degrees(pitch)
        yaw_deg = np.degrees(yaw)

        # Bin values
        bins = np.arange(-self.angle, self.angle, self.binwidth)
        binned_pose = np.digitize([pitch_deg, yaw_deg], bins) - 1

        # Create binned and regression labels
        binned_label = torch.tensor(binned_pose, dtype=torch.long)
        regression_label = torch.tensor([pitch_deg, yaw_deg], dtype=torch.float32)

        return image, binned_label, regression_label


def get_dataloaders(args, val_split=0.1) -> tuple:
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function properly handles dataset filtering by creating the dataset first,
    then splitting based on the filtered length to avoid index out-of-bounds errors.

    Args:
        - data: Root directory containing 'train' and 'test' subdirectories
        - batch_size: Batch size for all dataloaders
        - num_workers: Number of worker processes for data loading
        - angle: Maximum angle in degrees for filtering samples
        - binwidth: Width of angle bins for classification

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = args.data
    batch_size = args.batch_size
    num_workers = args.num_workers

    angle = args.angle
    binwidth = args.binwidth

    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    _ = transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ], seed=42)
    
    # Create training transform
    transform_train = GazeSafeAugmentation(image_size=96)

    # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(train_data_path, transform_train, angle, binwidth)
    full_val_dataset = CustomGazeDataset(train_data_path, transform_val, angle, binwidth)
    test_dataset = CustomGazeDataset(test_data_path, transform_test, angle, binwidth)
    
    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)

    # Create dataloaders
    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader
