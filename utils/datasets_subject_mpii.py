# === Standard Libraries ===
import os

# === Third-Party Libraries ===
import torch
import skimage.io
import numpy as np
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

# === Local Modules ===
from utils import util
from utils.augmentation import GazeSafeAugmentation, clahe


class CustomGazeDataset(Dataset):
    """
    MPIIGaze dataset with binned labels.

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self, data_path: str, transform=None, angle: int = 35):
        self.labels_dir = os.path.join(data_path, "Label")
        self.images_dir = os.path.join(data_path, "Image")

        label_files = [os.path.join(self.labels_dir, label) for label in os.listdir(self.labels_dir)]

        self.transform = transform
        self.orig_list_len = 0
        self.angle = angle
        self.lines = []

        pitch_deg_list = []
        yaw_deg_list = []
        for label_file in label_files:
            with open(label_file) as f:
                lines = f.readlines()[1:]  # Skip the header line
                self.orig_list_len += len(lines)
                for line in lines:
                    gaze2d = line.strip().split(" ")[7]
                    yaw, pitch = np.array(gaze2d.split(",")).astype("float")
                    pitch_deg, yaw_deg = np.degrees(pitch), np.degrees(yaw)
                    
                    pitch_deg_list.append(pitch_deg)
                    yaw_deg_list.append(yaw_deg)
                    
                    if abs(pitch_deg) <= self.angle and abs(yaw_deg) <= self.angle:
                        self.lines.append(line)

        removed_items = self.orig_list_len - len(self.lines)
        print(f"{removed_items} items removed from dataset that have an angle > {self.angle}")
        
        pitch_deg = np.array(pitch_deg_list)
        yaw_deg = np.array(yaw_deg_list)
        print(f"Pitch | min-avg-max: [{pitch_deg.min():.1f}, {pitch_deg.mean():.1f}, {pitch_deg.max():.1f}]")
        print(f"Yaw   | min-avg-max: [{yaw_deg.min():.1f}, {yaw_deg.mean():.1f}, {yaw_deg.max():.1f}]\n")
        
    def __len__(self):
        return len(self.lines)

    def __getitem__(self, idx):
        line = self.lines[idx].strip().split(" ")

        image_path = line[0]
        gaze2d = line[7]

        # Get labels (radians)
        yaw, pitch = np.array(gaze2d.split(",")).astype("float") # FIXME careful: yaw, pitch (MPII) pitch, yaw (ours)

        # Load face image
        face = skimage.io.imread(f"{self.images_dir}/{image_path}")
        
        # Apply transforms
        face = clahe(face)
        image = self.transform(image=face, eye_coords=None)["image"]

        # Get labels
        label = torch.stack([torch.tensor(pitch, dtype=torch.float32),
                             torch.tensor(yaw,   dtype=torch.float32)], dim=0)
        
        return image, label


def get_dataloaders(args, val_split=0.1, test_split=0.1) -> tuple:
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function properly handles dataset filtering by creating the dataset first,
    then splitting based on the filtered length to avoid index out-of-bounds errors.

    Args:
        - data: Root directory containing 'train' and 'test' subdirectories
        - batch_size: Batch size for all dataloaders
        - num_workers: Number of worker processes for data loading
        - angle: Maximum angle in degrees for filtering samples

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = args.data
    batch_size = args.batch_size
    num_workers = args.num_workers

    # Create val, test transforms
    _ = transform_val = transform_test = A.Compose([
        A.Resize(height=96, width=96),
        A.Normalize(),
        ToTensorV2()
    ], seed=42)
    
    # Create training transform
    transform_train = GazeSafeAugmentation(image_size=96)

    # # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(data_root, transform_train)
    full_val_dataset = CustomGazeDataset(data_root, transform_val)
    full_test_dataset = CustomGazeDataset(data_root, transform_test)
    
    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)
    indices = list(range(n))
    np.random.shuffle(indices)
    
    # Calculate split sizes
    test_size = int(n * test_split)
    val_size = int(n * val_split)
    train_size = n - (val_size + test_size)
    
    # Split indices in a more intuitive way
    train_idx = indices[:train_size]
    val_idx = indices[train_size:train_size+val_size]
    test_idx = indices[train_size+val_size:]
    
    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)
    test_dataset = torch.utils.data.Subset(full_test_dataset, test_idx)

    # Create dataloaders
    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader
