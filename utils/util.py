import os
import csv
import sys
import cv2
import yaml
import time
import copy
import math
import onnx
import numpy
import torch
import random
import argparse
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt

from pathlib import Path
from timeit import timeit
from loguru import logger
from functools import wraps
from platform import system
from onnxsim import simplify
from datetime import datetime


g = torch.Generator()
g.manual_seed(42)


def seed_worker(worker_id):
    """ Seed worker for DataLoader with 'g' """
    worker_seed = torch.initial_seed() % 2**32
    numpy.random.seed(worker_seed)
    random.seed(worker_seed)


def init_deterministic_seed(seed=42, deterministic_mode=True):
    """ Setup random seed """
    random.seed(seed)
    numpy.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.benchmark = not deterministic_mode
    torch.backends.cudnn.deterministic = deterministic_mode
    # torch.use_deterministic_algorithms(deterministic_mode)  # (optional), but enforces stricter control
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'   # [':4096:8', ':16:8'] for reproducibility
    os.environ['TORCH_DETERMINISTIC'] = '1'


def timer(func):
    """ Time decorator """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        time_taken = (end_time - start_time)

        if time_taken > 60:
            time_taken = f"{time_taken / 60.0:.2f} minutes"
        elif time_taken > 3600:
            time_taken = f"{time_taken / 3600.0:.2f} hours"
        elif time_taken > 86400:
            time_taken = f"{time_taken / 86400.0:.2f} days"
        else:
            time_taken = f"{time_taken:.2f} seconds"
        logger.warning(f"Function '{func.__name__}' took {time_taken} to complete.")
        return result
    return wrapper


def setup_multi_processes():
    """ Setup multi-processing environment variables """

    # Set multiprocess start method as `fork` to speed up the training
    if system() != 'Windows':
        torch.multiprocessing.set_start_method('fork', force=True)

    # Disable opencv multithreading to avoid system being overloaded (incompatible with PyTorch DataLoader)
    cv2.setNumThreads(0)

    # Setup OMP threads
    if 'OMP_NUM_THREADS' not in os.environ:
        os.environ['OMP_NUM_THREADS'] = '16'

    # Setup MKL threads
    if 'MKL_NUM_THREADS' not in os.environ:
        os.environ['MKL_NUM_THREADS'] = '16'


def evaluate_threads_runtime(max_num_threads=99):
    """ Evaluate how a runtime of matrix multiplication changes with the number of threads """
    runtimes = []
    threads = [1] + [t for t in range(2, max_num_threads, 2)]

    for i, thread in enumerate(threads):
        # Set current number of threads
        torch.set_num_threads(thread)

        # Calculate runtime for matrix multiplication
        runtime = timeit(
            setup="import torch; x = torch.randn(96, 96); y = torch.randn(96, 96)",
            stmt="torch.mm(x, y)",
            number=100,
        )
        # Save and show current results
        runtimes.append(runtime)
        logger.info(f"index={i},\tthread={thread},\truntime={runtime}")

    # Show final results
    index = runtimes.index(min(runtimes))
    logger.info(f"index={index},\tthread={threads[index]},\truntime={runtimes[index]}\tResult!")


def setup_logger(log_name='exp'):
    """ Setup a logger environments for different purposes

    LEVELS = [TRACE, DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL]
    show messages:
        logger.trace("A trace message.")
        logger.debug("A debug message.")
        logger.info("An info message.")
        logger.success("A success message.")
        logger.warning("A warning message.")
        logger.error("An error message.")
        logger.critical("A critical message.")

        colorize=None --> the choice is automatically made based on the sink being a tty or not.
    """
    folder = "loggers"
    Path(folder).mkdir(parents=True, exist_ok=True)
    cur_date_time = datetime.now().strftime("%d.%m.%Y-%H-%M-%S")

    # For terminal - configuration to stderr (Optionally)
    logger.remove(0)    # To remove default version of logger
    default_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    logger.add(sink=sys.stderr, level='INFO', format=default_format, filter=None, colorize=None, serialize=False, backtrace=True, diagnose=True, enqueue=False, context=None, catch=True)

    # For logger file - configuration
    log_path = os.path.join(folder, f"{log_name}_{cur_date_time}.log")
    log_format = "{time:YYYY-MM-DD HH:mm:ss} | <level>{level: <8}</level> | <level>{message}</level>"
    logger.add(sink=log_path, level="TRACE", format=log_format, colorize=None, rotation="10 MB")


def strip_optimizer(filename):
    x = torch.load(filename, map_location=torch.device('cpu'))
    x['model'].half()   # to FP16
    for p in x['model'].parameters():
        p.requires_grad = False
    torch.save(x, filename)


def load_weight(ckpt, model):
    dst = model.state_dict()
    src = torch.load(ckpt, 'cpu')['model'].float().state_dict()

    ckpt = {}
    for k, v in src.items():
        if k in dst and v.shape == dst[k].shape:
            ckpt[k] = v
    model.load_state_dict(state_dict=ckpt, strict=False)
    return model


def weight_decay(model, decay=1e-4):
    """ Param groups weight decay """
    p1, p2 = [], []
    for name, param in model.named_parameters():
        if not param.requires_grad:
            continue

        if len(param.shape) == 1 or name.endswith(".bias"):
            p1.append(param)
        else:
            p2.append(param)

    return [{'params': p1, 'weight_decay': 0.0},
            {'params': p2, 'weight_decay': decay}]


def clip_gradients(model, max_norm=10.0):
    parameters = model.parameters()
    torch.nn.utils.clip_grad_norm_(parameters, max_norm=max_norm)


def set_experiment_results_output(args, root="runs"):
    experiment_path = ""

    if args.save_dir is not None:
        root = args.save_dir

    if os.path.exists(root):
        exps = os.listdir(root)
        exps_index = [int(exp.split("_")[-1]) for exp in exps if exp.startswith("exp_") and exp.split("_")[-1].isdigit()]
        next_index = max(exps_index) + 1 if exps_index else 0
        experiment_path = os.path.join(root, f"exp_{next_index}")
    else:
        experiment_path = os.path.join(root, f"exp_0")

    os.makedirs(experiment_path, exist_ok=True)
    
    return experiment_path


def safe_yaml_config_file(args):
    """ Save the training config file in yaml format """
    clean_args = vars(args)
    with open(os.path.join(args.save_dir, "training_config_pipeline.yaml"), 'w') as yaml_file:
        yaml.dump(clean_args, yaml_file, default_flow_style=False)


def set_experiment_logger(args):
    """ Create a logger file in experiment logger """
    cur_date_time = datetime.now().strftime("%d.%m.%Y-%H-%M-%S")
    log_path = os.path.join(args.output_dir, f"{cur_date_time}.log")
    log_format = "{time:YYYY-MM-DD HH:mm:ss} | <level>{level: <8}</level> | <level>{message}</level>"
    logger.add(sink=log_path, level="TRACE", format=log_format, colorize=None, rotation="10 MB")


def save_results_csv(args, training_results, csv_file_name="training_results.csv"):
    save_path = os.path.join(args.output_dir, csv_file_name)

    # Define the CSV file's headers
    headers = ["epoch",
               "train_loss", "val_loss", "val_loss_ema",
               "train_F1score", "val_F1score", "val_F1score_ema",
               "best_F1score", "best_F1score_ema", "lr", "status", "status_ema"]

    # Writing to the CSV file
    with open(save_path, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=headers)
        writer.writeheader()
        for result in training_results:
            writer.writerow(result)


def symplify_onnx_model(onnx_path):
    # Load your predefined ONNX model
    model = onnx.load(onnx_path)

    # Convert model
    model_simplified, check = simplify(model)
    assert check, "Simplified ONNX model could not be validated"

    # Save simplified model
    onnx.save(model_simplified, onnx_path)


def check_onnx_model(onnx_path, verbose=False):
    # Load the ONNX model
    model = onnx.load(onnx_path)

    # Check that the model is well formed
    onnx.checker.check_model(model, full_check=True)

    # Print a human readable representation of the graph
    if verbose: print(onnx.helper.printable_graph(model.graph))


def export_onnx_model(args, model, example_input, onnx_path, simplify=True, verify=True):
    """ Save the model in ONNX format """
    export_params = True
    do_constant_folding = True
    input_names = ["input"]
    output_names = ["output"]
    dynamic_axes = None
    verbose = False

    if dynamic_axes:
        dynamic_axes = {
            input_names[0]: {0: 'batch_size'},
            output_names[0]: {0: 'batch_size'}
        }

    # Export the PyTorch model to ONNX
    torch.onnx.export(
        model=model,                                # model being run
        args=example_input,                         # model input (or a tuple for multiple inputs)
        f=onnx_path,                                # where to save the model
        export_params=export_params,                # store the trained parameter weights inside the model
        opset_version=args.opset_version,           # the ONNX version to export the model to
        do_constant_folding=do_constant_folding,    # to execute constant folding for optimization
        input_names=input_names,                    # specify the names of input
        output_names=output_names,                  # specify the names of output
        verbose=verbose,                            # prints a description of the model being exported to stdout
        dynamic_axes=dynamic_axes                   # variable length axes
    )

    if simplify: symplify_onnx_model(onnx_path)
    if verify: check_onnx_model(onnx_path)


def save_result(args, epoch, model, acc, loss, train_acc, train_loss, save=False, EMA=""):
    """ Save loggers """
    type_set, status_type, ema = (EMA, EMA, f"_{EMA}") if EMA == "EMA" else ("val", " "*len("EMA"), "")
    storage = {}
    
    if acc > storage[type_set]["best_acc"]:
        storage[type_set]["status"] = "best-acc"
        storage[type_set]["best_epoch"] = epoch
        storage[type_set]["best_acc"] = acc
        storage[type_set]["best_loss"] = loss
        storage[type_set]["best_train_acc"] = train_acc
        storage[type_set]["best_train_loss"] = train_loss
    elif (acc == storage[type_set]["best_acc"]) and (loss < storage[type_set]["best_loss"]):
        storage[type_set]["status"] = "best-acc-loss"
        storage[type_set]["best_epoch"] = epoch
        storage[type_set]["best_acc"] = acc
        storage[type_set]["best_loss"] = loss
        storage[type_set]["best_train_acc"] = train_acc
        storage[type_set]["best_train_loss"] = train_loss
    else:
        storage[type_set]["status"] = "pass"
    
    # Get log info
    epoch_info = f"epoch: {str(epoch).zfill(3)}"
    acc = f"acc: {round(acc, 8):<11}"
    loss = f"loss: {round(loss, 8):<11}"
    train_acc = f"train_acc: {round(train_acc, 8):<11}"
    train_loss = f"train_loss: {round(train_loss, 8):<11}"
    status = f"status: {status_type} {storage[type_set]['status']}"

    best_epoch = f"epoch: {str(storage[type_set]['best_epoch']).zfill(3)}"
    best_acc = f"acc: {round(storage[type_set]['best_acc'], 8):<11}"
    best_loss = f"loss: {round(storage[type_set]['best_loss'], 8):<11}"
    best_train_acc = f"train_acc: {round(storage[type_set]['best_train_acc'], 8):<11}"
    best_train_loss = f"train_loss: {round(storage[type_set]['best_train_loss'], 8):<11}"
    best_status = f"status: {status_type} Best epoch"

    # Save logs
    logger.debug(f"{epoch_info}  |  {acc}  |  {loss}  |  {train_acc}  |  {train_loss}  |  {status}")
    if args.epochs == epoch:
        if type_set == "val": logger.trace("\n")
        logger.success(f"{best_epoch}  |  {best_acc}  |  {best_loss}  |  {best_train_acc}  |  {best_train_loss}  |  {best_status}")

    if not save:
        return storage
    
    # Save model
    save_path = os.path.join(args.output_dir, "weights")
    os.makedirs(save_path, exist_ok=True)

    if hasattr(model, 'module'):
        model = model.module

    states = {
        "16": {'model': copy.deepcopy(model).half()},
        "32": {'model': copy.deepcopy(model)},
    }

    for key, state in states.items():
        # Save the model on the last epoch
        torch.save(state, f"{save_path}/last_{key}{ema}.pt")

        if "best" in storage[type_set]["status"]:
            if (epoch > 200):
                # Save models on the best epochs
                torch.save(state, f"{save_path}/epoch_{epoch}_{key}{ema}.pt")
            # Save the model on the best epoch
            torch.save(state, f"{save_path}/best_{key}{ema}.pt")

            # Input example to trace the model
            example_input = torch.randn(1, args.input_channels, args.input_size, args.input_size, device=args.device)
            if key == "16":
                example_input = example_input.half()
            
            # For a traced model
            traced_model = torch.jit.trace(state["model"], example_input)
            torch.jit.save(traced_model, f"{save_path}/best_traced_{key}{ema}.pt")

            # For a scripted model
            scripted_model = torch.jit.script(state["model"])
            torch.jit.save(scripted_model, f"{save_path}/best_scripted_{key}{ema}.pt")

            # Save onnx model
            if key == "32":
                export_onnx_model(args, state["model"], example_input, f"{save_path}/best_onnx_{key}{ema}.onnx")
    return storage


class ColoredTqdmFile(object):
    """ Wrapper around a file object that adds color codes to the write method """
    def __init__(self, file, color_code):
        self._file = file
        self._reset_code = '\033[0m'
        self._color_code = {
            "RED": '\033[91m',
            "BLUE": '\033[94m',
            "GREEN": '\033[32m',
            "YELLOW": '\033[33m',
        }[color_code]

    def write(self, s):
        self._file.write(f'{self._color_code}{s}{self._reset_code}')

    def flush(self):
        self._file.flush()


# =============================================================================
def get_custom_learning_rate(args, epoch):
    """
    choices=['step','poly','exponential', 'cosine']
    cosine:         good, but decay lr to slow
    step:           good, you have to manually specify milestones
    exponential:    good, looks like step with auto milestones (better then step?)
    poly:           good, looks like smoothed exponential (better then exponential?)

    step        (args.epochs = 500, args.multistep_gamma = 0.5, args.milestones_step = 20)
    exponential (args.epochs = 500, args.multistep_gamma = 0.9, args.step_size = 3)
    poly        (args.epochs = 500, args.polystep_power = 12)
    """
    cur_lr = args.lr

    if args.warmup_epochs and (epoch <= args.warmup_epochs):
        cur_lr = epoch * args.lr / args.warmup_epochs
        if epoch == 0 and args.warmup_factor is not None:
            cur_lr = max(cur_lr, args.lr * args.warmup_factor)

    elif args.scheduler == 'step':
        num_milestones = 0
        for m in args.milestones:
            num_milestones += (1 if epoch >= m else 0)
        cur_lr = args.lr * (args.multistep_gamma ** num_milestones)

    elif args.scheduler == 'poly':
        epoch_frac = (args.epochs - epoch) / args.epochs
        epoch_frac = max(epoch_frac, 0)
        cur_lr = args.lr * (epoch_frac ** args.polystep_power)

    elif args.scheduler == 'exponential':
        cur_lr = args.lr * (args.multistep_gamma ** (epoch//args.step_size))

    elif args.scheduler == 'cosine':
        if epoch == 0:
            cur_lr = args.lr
        else:
            lr_min = 0
            cur_lr = (args.lr - lr_min) * (1 + math.cos(math.pi * epoch / args.epochs)) / 2.0 + lr_min

    return cur_lr


def plot_custom_LR_scheduler(args_original, show=False):
    """ Visualize LR-scheduler """
    parser = argparse.ArgumentParser()
    parser.add_argument('--output-dir', type=str, default="learning_rates")
    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    args.epochs = 10
    args.milestones_step = 10
    milestones = list(range(args.milestones_step, args.epochs+1, args.milestones_step))

    args.warmup_epochs = 0
    args.warmup_factor = 1e-3

    args.lr = 0.00001
    args.scheduler = 'poly'             # choices=['step','poly','exponential', 'cosine']
    args.milestones = milestones        # epochs at which learning rate is divided ('step')
    args.multistep_gamma = 0.4          # multi step gamma (default: 0.1) ('step', 'exponential')
    args.step_size = 3                  # step size for exp lr decay ('exponential')
    args.polystep_power = 5             # poly step gamma (default: 1.0) ('poly')

    save_path = os.path.join(args.output_dir, f"lr_scheduler_{args.scheduler}.svg")

    lr_steps = list()
    epochs = list(range(args.epochs))

    for i, epoch in enumerate(epochs):
        cur_lr = get_custom_learning_rate(args, epoch)
        lr_steps.append(cur_lr)
        if show: logger.info(f"{i}: {cur_lr}")

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title(f'LR scheduler: {args.scheduler}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, lr_steps, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()
# =============================================================================


def plot_lr(epochs, optimizer, scheduler, save_path=""):
    """ Plot learning rate schedule """
    save_path = os.path.join(save_path, "lr_scheduler.svg")

    optimizer = copy.copy(optimizer)
    scheduler = copy.copy(scheduler)

    lr_steps = []
    epochs = list(range(1, epochs+1))

    for epoch in epochs:
        scheduler.step(epoch-1)
        lr_steps.append(optimizer.param_groups[0]['lr'])
    scheduler.step(0)

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title(f'LR scheduler: {scheduler.__class__.__name__}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, lr_steps, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()


def plot_actual_learning_rate(args, results):
    save_path = os.path.join(args.output_dir, f"actual_learning_rate.svg")
    
    # Extract epoch numbers, accuracies, and losses for training and validation
    epochs, learning_rates = zip(*results)

    # Initialize the plot
    plt.figure(figsize=(20, 12))
    plt.title('Actual learning rate', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    plt.ylabel('LR', fontsize=16)

    # Plot the training and validation accuracies
    plt.plot(epochs, learning_rates, label='LR scheduler', marker='o')

    # Show grid, legend, and labels
    plt.grid(True)
    plt.legend()
    plt.savefig(save_path, dpi=600)
    plt.close()


def plot_performance_results(args, performance_results):
    save_path = os.path.join(args.save_dir, f"performance_results.svg")
    
    # Extract epoch numbers, accuracies, and losses for training and validation
    epochs, train_losses, val_losses, train_angular_errors, val_angular_errors= zip(*performance_results)

    best_train_angular_errors = min(train_angular_errors)
    best_val_angular_errors = min(val_angular_errors)
    stop_epoch = val_angular_errors.index(best_val_angular_errors)
    train_stop_angular_errors = train_angular_errors[stop_epoch]

    best_train_loss = min(train_losses)
    best_val_loss = min(val_losses)
    stop_epoch_loss = val_losses.index(best_val_loss)
    train_stop_loss = train_losses[stop_epoch_loss]
    
    results = "Best epoch={0}-E/{1}-L, V-loss={2:.5f}, Errors: TB={3:.3f}, T={4:.3f}, V={5:.3f}".format(
        stop_epoch,
        stop_epoch_loss,
        best_val_loss,
        best_train_angular_errors,
        train_stop_angular_errors,
        best_val_angular_errors
    )
    
    # Initialize the plot
    fig, ax1 = plt.subplots(figsize=(20, 12))
    # After defining `ax1` and before plotting:
    ax1.set_ylim(0, 4)  # Clamp angular error to 0–10 degrees

    plt.title(f'Results: {results}', fontsize=20)
    plt.xlabel('Epoch', fontsize=16)
    ax1.set_ylabel('Angular Error', fontsize=16)

    # Plot the training and validation accuracies
    ax1.plot(epochs, train_angular_errors, label='Train Error', marker='o')
    ax1.plot(epochs, val_angular_errors, label='Val Error', marker='o')
    ax1.tick_params(axis='y')

    ax1.scatter(x=stop_epoch, y=best_val_angular_errors, s=50, color='k', zorder=5.5)
    ax1.axhline(y=best_val_angular_errors, color='k', linestyle='--', linewidth=2.0)
    ax1.axvline(x=stop_epoch, color='k', linestyle='--', linewidth=2.0)

    # Create a second y-axis for the loss
    ax2 = ax1.twinx()
    ax2.set_ylim(0, 1.5)  # Adjust based on your typical loss range
    
    ax2.set_ylabel('Loss', fontsize=16, color='r')
    ax2.plot(epochs, train_losses, color='b', linestyle='--', marker='o', label='Train Loss', alpha=0.6)
    ax2.plot(epochs, val_losses, color='r', linestyle='--', marker='o', label='Val Loss')
    ax2.tick_params(axis='y', labelcolor='r')

    ax2.scatter(x=stop_epoch_loss, y=best_val_loss, s=50, color='k', zorder=5.5)
    ax2.axhline(y=best_val_loss, color='k', linestyle='--', linewidth=2.0)
    ax2.axvline(x=stop_epoch_loss, color='r', linestyle='--', linewidth=1.0, alpha=0.6)

    # Show grid, legend, and labels
    ax1.grid(True)
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines + lines2, labels + labels2, loc='center right')

    plt.savefig(save_path, dpi=600)
    plt.close()


def plot_performance_results_splited(args, performance_results):
    loss_save_path = os.path.join(args.save_dir, "performance_results_loss.svg")
    error_save_path = os.path.join(args.save_dir, "performance_results_error.svg")
    
    # Unpack values
    epochs, train_losses, val_losses, train_angular_errors, val_angular_errors = zip(*performance_results)

    # Best metrics for annotation
    best_val_error = min(val_angular_errors)
    stop_epoch_error = val_angular_errors.index(best_val_error)
    train_error_at_best = train_angular_errors[stop_epoch_error]

    best_val_loss = min(val_losses)
    stop_epoch_loss = val_losses.index(best_val_loss)
    train_loss_at_best = train_losses[stop_epoch_loss]
    
    # Calculate the y-axis limit for the loss plot
    e = 10
    if len(epochs) > e:
        angular_error_ylim = max(max(train_angular_errors[:e]), max(val_angular_errors[:e]))
        loss_ylim = max(max(train_losses[:e]), max(val_losses[:e]))

    # ---------- Plot Angular Error ----------
    plt.figure(figsize=(20, 12))
    plt.title(f"Angular Error | Best Val: {best_val_error:.3f} at Epoch {stop_epoch_error}", fontsize=20)
    plt.xlabel("Epoch", fontsize=16)
    plt.ylabel("Angular Error (degrees)", fontsize=16)
    if len(epochs) > e: plt.ylim(0, angular_error_ylim)
    plt.grid(True)

    plt.plot(epochs, train_angular_errors, label="Train Error", marker='o')
    plt.plot(epochs, val_angular_errors, label="Val Error", marker='o')

    plt.scatter(stop_epoch_error, best_val_error, color='k', zorder=5.5)
    plt.axhline(y=best_val_error, color='k', linestyle='--', linewidth=2.0)
    plt.axvline(x=stop_epoch_error, color='k', linestyle='--', linewidth=2.0)

    plt.legend()
    plt.tight_layout()
    plt.savefig(error_save_path, dpi=600)
    plt.close()

    # ---------- Plot Loss ----------
    plt.figure(figsize=(20, 12))
    plt.title(f"Loss | Best Val: {best_val_loss:.5f} at Epoch {stop_epoch_loss}", fontsize=20)
    plt.xlabel("Epoch", fontsize=16)
    plt.ylabel("Loss", fontsize=16)
    if len(epochs) > e: plt.ylim(0, loss_ylim)
    plt.grid(True)

    plt.plot(epochs, train_losses, label="Train Loss", marker='o', linestyle='--', alpha=0.6)
    plt.plot(epochs, val_losses, label="Val Loss", marker='o', linestyle='--')

    plt.scatter(stop_epoch_loss, best_val_loss, color='k', zorder=5.5)
    plt.axhline(y=best_val_loss, color='k', linestyle='--', linewidth=2.0)
    plt.axvline(x=stop_epoch_loss, color='r', linestyle='--', linewidth=1.0, alpha=0.6)

    plt.legend()
    plt.tight_layout()
    plt.savefig(loss_save_path, dpi=600)
    plt.close()


def get_alpha_cosine(epoch, max_epochs, alpha_start=0.9, alpha_end=0.1):
    """ Cosine annealing for weighted losses """
    cosine = 0.5 * (1 + math.cos(math.pi * epoch / max_epochs))
    alpha = alpha_end + (alpha_start - alpha_end) * cosine
    return alpha


def get_params(model, part="stem", require_grad_only=False):
    # architecture = {
    #     "stem":     [model.conv1, model.bn1],
    #     "backbone": [model.layer1, model.layer2, model.layer3, model.layer4],
    #     "layer1":   [model.layer1],
    #     "layer2":   [model.layer2],
    #     "layer3":   [model.layer3],
    #     "layer4":   [model.layer4],
    #     "head":     [model.fc_yaw, model.fc_pitch],
    # }
    
    architecture = {
        "cnn_face":     [model.cnn_face],
        "fc_face":      [model.fc_face],
        # "fc_yaw":       [model.fc_yaw],
        # "fc_pitch":     [model.fc_pitch],
    }
    
    for module in architecture[part]:
        for _, sub_module in module.named_modules():
            for _, param in sub_module.named_parameters():
                if not require_grad_only or param.requires_grad:
                    yield param


def freeze_batchnorm_layers(model):
    for module in model.modules():
        if isinstance(module, torch.nn.BatchNorm2d):
            module.eval()
            for param in module.parameters():
                param.requires_grad = False


def adjust_lr(optimizer, new_lr, lr_gamma=0.6, min_lr=1e-6):
    """ Set more stable learning rate for late epochs """
    new_lr = max(new_lr * lr_gamma, min_lr)
    for param_group in optimizer.param_groups:
        param_group['lr'] = new_lr
    return optimizer


def LR_weights_callback(model, best_model_weights, optimizer, current_lr, fail_epochs, save=False):
    """ Callback weights and decrease LR """
   
    if save:
        fail_epochs = 0
        best_model_weights = copy.deepcopy(model.state_dict())

    elif (fail_epochs+1 >= 3) and (current_lr > 1e-4):
        fail_epochs = 0
        model.load_state_dict(best_model_weights)
        optimizer = adjust_lr(optimizer, current_lr, lr_gamma=0.7, min_lr=1e-6)

    elif (fail_epochs+1 >= 10) and (current_lr > 1e-5):
        fail_epochs = 0
        model.load_state_dict(best_model_weights)
        optimizer = adjust_lr(optimizer, current_lr, lr_gamma=0.7, min_lr=1e-6)

    elif (fail_epochs+1 >= 15) and (current_lr <= 1e-5):
        fail_epochs = 0
        optimizer = adjust_lr(optimizer, current_lr, lr_gamma=0.7, min_lr=1e-6)
    else:
        fail_epochs += 1

    return best_model_weights, fail_epochs


if __name__ == '__main__':
    evaluate_threads_runtime()
